package vip.xiaonuo.bugly.core.enums;

import lombok.Getter;

/**
 * 系统内置的不可删除的标识枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/1/17 15:25
 */
@Getter
public enum BuglyBuildInEnum {
    /**
     * 超管用户账号
     */
    BUILD_IN_USER_ACCOUNT("superAdmin", "超管");

    private final String value;

    private final String name;

    BuglyBuildInEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }
}
