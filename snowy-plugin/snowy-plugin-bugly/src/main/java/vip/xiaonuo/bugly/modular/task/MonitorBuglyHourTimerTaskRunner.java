package vip.xiaonuo.bugly.modular.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import vip.xiaonuo.bugly.modular.alarm.service.BuglyAlarmService;
import vip.xiaonuo.bugly.modular.message.service.MessageService;
import vip.xiaonuo.bugly.modular.monitor.service.MonitorBuglyHourService;
import vip.xiaonuo.common.timer.CommonTimerTaskRunner;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;

/**
 * Bugly监控数据导入定时任务(小时)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/1/18 17:40
 */
@Component
@Slf4j
public class MonitorBuglyHourTimerTaskRunner implements CommonTimerTaskRunner {

    @Resource
    private MonitorBuglyHourService monitorBuglyHourService;

    @Resource
    private MessageService messageService;

    @Resource
    private BuglyAlarmService alarmService;

    @Override
    public void action() {
        try {
            LocalDateTime startTime = LocalDateTime.now();
            log.info("开始执行导入Bugly2.0监控数据(小时)(汇总数据)定时任务......");
            // 发送心跳数据异常告警
            monitorBuglyHourService.isHourHeartbeatDataAb(null, Boolean.TRUE);
            // 发送崩溃数据异常告警
            monitorBuglyHourService.isHourCrashDataAb(null, Boolean.TRUE);
            // 导入数据
            Boolean importResult = monitorBuglyHourService.importMonitorData(null, "hour");
            LocalDateTime endTime = LocalDateTime.now();
            Duration duration = Duration.between(startTime, endTime);
            if (importResult) {
                log.info(String.format("执行导入Bugly2.0监控数据(小时)(汇总数据)定时任务成功,耗时=[%s]ms。", duration.toMillis()));
                // 发送监控告警(小时)
                alarmService.sendMonitorHourAlarm(null);
            } else {
                messageService.sendXingeAdministratorMessage("执行导入Bugly2.0监控数据(小时)(汇总数据)定时任务失败", null);
                log.warn(String.format("执行导入Bugly2.0监控数据(小时)(汇总数据)定时任务失败,耗时=[%s]ms。", duration.toMillis()));
            }
        } catch (Exception e) {
            messageService.sendXingeAdministratorMessage("执行导入Bugly2.0监控数据(小时)(汇总数据)定时任务失败", null);
            log.error("执行导入Bugly2.0监控数据(小时)(汇总数据)定时任务失败!", e);
        }
    }
}
