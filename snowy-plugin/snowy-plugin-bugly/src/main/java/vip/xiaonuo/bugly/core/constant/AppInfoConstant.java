package vip.xiaonuo.bugly.core.constant;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * App信息相关常量
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/1/31 10:47
 */
public class AppInfoConstant implements Serializable {

    private static final Long serialVersionUID = 1L;
    /**
     * 应用平台类型和平台名称
     * 平台类型 0:Android 1:iOS 2:Windows
     */
    public static final Map<Long, String> APP_PLATFORM_NAME = new LinkedHashMap<>();

    /**
     * 告警周期和名称
     * 统计周期(最近1小时=hour,最近一天=day,最近1小时数据校验=hour_ab,最近一天数据校验=day_ab)
     */
    public static final Map<String, String> ALARM_TYPE_NAME = new LinkedHashMap<>();

    /**
     * 条件值类型和名称
     */
    public static final Map<String, String> VALUE_TYPE_NAME = new LinkedHashMap<>();

    static {
        APP_PLATFORM_NAME.put(0L, "Android");
        APP_PLATFORM_NAME.put(1L, "iOS");
        APP_PLATFORM_NAME.put(2L, "Windows");
        APP_PLATFORM_NAME.put(3L, "H5");
        APP_PLATFORM_NAME.put(4L, "鸿蒙");
    }

    static {
        ALARM_TYPE_NAME.put("hour", "小时");
        ALARM_TYPE_NAME.put("day", "天");
        ALARM_TYPE_NAME.put("hour_ab", "最近1小时数据校验");
        ALARM_TYPE_NAME.put("day_ab", "最近一天数据校验");
    }

    static {
        VALUE_TYPE_NAME.put("1", "自定义值");
        VALUE_TYPE_NAME.put("2", "最近7日平均值(相同小时)");
        VALUE_TYPE_NAME.put("3", "最近7日去掉最高和最低平均值(相同小时)");
        VALUE_TYPE_NAME.put("4", "相对前一天变化率(相同小时)");
        VALUE_TYPE_NAME.put("5", "相对最近7日平均值变化率(相同小时)");
        VALUE_TYPE_NAME.put("6", "相对最近7日去掉最高和最低平均值变化率(相同小时)");
        VALUE_TYPE_NAME.put("7", "相对最近7日去掉最高和最低正态分布1个标准差σ的变化率(相同小时)");
        VALUE_TYPE_NAME.put("8", "相对最近7日去掉最高和最低正态分布2个标准差σ的变化率(相同小时)");
        VALUE_TYPE_NAME.put("9", "相对最近7日去掉最高和最低正态分布3个标准差σ的变化率(相同小时)");

        VALUE_TYPE_NAME.put("10", "相对最近48小时去掉最高和最低正态分布1个标准差σ的变化率(相同小时)");
        VALUE_TYPE_NAME.put("11", "相对最近48小时去掉最高和最低正态分布2个标准差σ的变化率(相同小时)");
        VALUE_TYPE_NAME.put("12", "正态分布3σ变化率");

        VALUE_TYPE_NAME.put("17", "相对最近7日去掉最高和最低正态分布3个标准差σ的变化率(天维度)");

    }
}
