package vip.xiaonuo.bugly.core.constant;

import java.io.Serializable;

/**
 * 数据源相关常量
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/3/1 15:20
 */
public class DataSourceConstant implements Serializable {

    private static final Long serialVersionUID = 1L;

    /**
     * 数据源名-默认数据库
     */
    public static final String DYNAMIC_DATASOURCE_MASTER = "master";

    /**
     * 数据源名-业务数据库
     */
    public static final String DYNAMIC_DATASOURCE_BIZ = "biz";

    /**
     * 数据源名-监控数据库
     */
    public static final String DYNAMIC_DATASOURCE_MONITOR = "monitor";

    /**
     * 数据源名-安全策略下发联动数据库
     */
    public static final String DYNAMIC_DATASOURCE_CLOUD = "cloud";

    /**
     * 数据源名-DPS样本采集数据库
     */
    public static final String DYNAMIC_DATASOURCE_COLLECTION = "collection";

    /**
     * 数据源名-jjneural数据库
     */
    public static final String DYNAMIC_DATASOURCE_JJNEURAL = "jjneural";

    /**
     * 数据源名-clickhouse数据库
     */
    public static final String DYNAMIC_DATASOURCE_CLICKHOUSE = "clickhouse";

    /**
     * 数据源名-bugly监控数据库
     */
    public static final String DYNAMIC_DATASOURCE_BUGLY_MONITOR = "buglymonitor";
}
