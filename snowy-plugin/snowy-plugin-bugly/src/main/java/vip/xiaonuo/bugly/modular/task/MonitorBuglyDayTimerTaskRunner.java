package vip.xiaonuo.bugly.modular.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import vip.xiaonuo.bugly.modular.alarm.service.BuglyAlarmService;
import vip.xiaonuo.bugly.modular.message.service.MessageService;
import vip.xiaonuo.bugly.modular.monitor.service.MonitorBuglyHourService;
import vip.xiaonuo.common.timer.CommonTimerTaskRunner;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;

/**
 * Bugly监控数据导入定时任务(天)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/1/18 17:44
 */
@Component
@Slf4j
public class MonitorBuglyDayTimerTaskRunner implements CommonTimerTaskRunner {

    @Resource
    private MonitorBuglyHourService monitorBuglyHourService;

    @Resource
    private MessageService messageService;

    @Resource
    private BuglyAlarmService alarmService;

    @Override
    public void action() {
        try {
            LocalDateTime startTime = LocalDateTime.now();
            log.info("开始执行导入监控数据(天)(汇总数据)定时任务......");
            Boolean importResult = monitorBuglyHourService.importMonitorData(null, "day");
            // 重试一次
            if (!importResult) {
                long periodMillis = 3000;
                messageService.sendXingeAdministratorMessage(String.format("第一次执行导入Bugly2.0监控数据(天)(汇总数据)定时任务失败，将在[%s]秒后重试一次!", periodMillis / 1000), null);
                log.warn(String.format("第一次执行导入Bugly2.0监控数据(天)(汇总数据)定时任务失败，将在[%s]秒后重试一次!", periodMillis / 1000));

                try {
                    Thread.sleep(periodMillis);
                } catch (InterruptedException e1) {
                    e1.printStackTrace();
                }
                importResult = monitorBuglyHourService.importMonitorData(null, "day");
            }

            LocalDateTime endTime = LocalDateTime.now();
            Duration duration = Duration.between(startTime, endTime);
            if (importResult) {
                log.info(String.format("执行导入监控数据(天)(汇总数据)定时任务成功,耗时=[%s]ms。", duration.toMillis()));
                // 发送监控告警(天)
                alarmService.sendMonitorDayAlarm(null);
            } else {
                messageService.sendXingeAdministratorMessage("执行导入监控数据(天)(汇总数据)定时任务失败", null);
                log.warn(String.format("执行导入监控数据(天)(汇总数据)定时任务失败,耗时=[%s]ms。", duration.toMillis()));
            }
        } catch (Exception e) {
            messageService.sendXingeAdministratorMessage("执行导入监控数据(天)(汇总数据)定时任务失败", null);
            log.error("执行导入监控数据(天)(汇总数据)定时任务失败!", e);
        }
    }
}
