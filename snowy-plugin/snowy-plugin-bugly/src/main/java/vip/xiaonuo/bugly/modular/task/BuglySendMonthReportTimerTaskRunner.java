package vip.xiaonuo.bugly.modular.task;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import vip.xiaonuo.bugly.modular.message.service.MessageService;
import vip.xiaonuo.bugly.modular.report.service.ReportService;
import vip.xiaonuo.common.timer.CommonTimerTaskRunner;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Bugly2.0Bugly2.0月报发送定时任务(月)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/10/31 10:54
 */
@Component
@Slf4j
public class BuglySendMonthReportTimerTaskRunner implements CommonTimerTaskRunner {

    @Resource
    private MessageService messageService;

    @Resource
    private ReportService reportService;

    @Override
    public void action() {
        try {
            LocalDateTime startTime = LocalDateTime.now();
            log.info("开始执行Bugly2.0月报发送(月)定时任务......");
            // 获取月报发送应用及接收人列表
            List<Map<String, List<String>>> appIdsAndUserIdsList = this.getMonthReportSendEmailToUserIds();
            log.info("月报收件人列表=[{}]", JSONObject.toJSONString(appIdsAndUserIdsList));
            if (CollectionUtils.isNotEmpty(appIdsAndUserIdsList)) {
                // 遍历各应用月报发送列表
                for (Map<String, List<String>> map : appIdsAndUserIdsList) {
                    List<String> appIds = map.get("appIds");
                    List<String> emailToUserIds = map.get("emailToUserIds");
                    List<String> emailCCUserIds = map.get("emailCCUserIds");
                    // Bugly2.0月报发送(月)
                    boolean sendResult = reportService.sendMonthReport(null, null, appIds, emailToUserIds, emailCCUserIds);
                    if (!sendResult) {
                        log.error("发送Bugly2.0月报失败!月报收件人列表=[{}]", JSONObject.toJSONString(map));
                        messageService.sendXingeAdministratorMessage(String.format("发送Bugly2.0月报失败!月报收件人列表=[%s]", JSONObject.toJSONString(map)), null);
                    }
                }
            }
            LocalDateTime endTime = LocalDateTime.now();
            Duration duration = Duration.between(startTime, endTime);
            log.info(String.format("执行Bugly2.0月报发送(月)定时任务成功,耗时=[%s]ms。", duration.toMillis()));
        } catch (Exception e) {
            messageService.sendXingeAdministratorMessage("执行Bugly2.0月报发送(月)定时任务失败", null);
            log.error("执行Bugly2.0月报发送(月)定时任务失败!", e);
        }
    }

    /**
     * 获取月报发送应用及接收人
     *
     * @return 月报发送应用及接收人
     */
    private List<Map<String, List<String>>> getMonthReportSendEmailToUserIds() {
        List<Map<String, List<String>>> appIdsAndUserIdsList = new ArrayList<>();
        String monthReportSendEmailToUserIds = "a3504ba50f:5050,8248:8980,8892,8395,9114,8257,5323,8193,98,5213;89754a9c4f:5050,8248:8980,8892,8395,9114,8257,5323,8193,98,5213;ccb40a6032:4346,4636,10497,456,6259,8032,9494:9802,8257,5323,8193,98,5213;fd6832fbce:4346,4636,10497,456,6259,8032,9494:8257,5323,8193,98,5213;eb2f9e911c:6390,6142:6385,8395,9114,7756,8257,5323,8193,98,5213,7019;h5_lord_cgid,h5_mtlord_cgid,h5_douyin_cgid,h5_wxchess_cgid,h5_ttchess_cgid,h5_mahjong_cgid,h5_mtmahjong_cgid,h5_hlmahjong_cgid,h5_mtchess_cgid,h5_ttmahjong_cgid,h5_ctriplord_cgid,h5_ctripmahjong_cgid,h5_kschess_cgid,h5_jdmahjong_cgid,h5_wxyuanqqs_cgid,h5_wxhdsl_cgid,h5_wxguandan_cgid,h5_jdlord_cgid,h5_jdguandan_cgid,h5_wxjjmatch_cgid:25,5239,7068:28,7305,8395,9114,8257,5323,8193,98,5213";
        monthReportSendEmailToUserIds = StringUtils.deleteWhitespace(monthReportSendEmailToUserIds);
        if (StringUtils.isNotBlank(monthReportSendEmailToUserIds)) {
            String[] appIds = StringUtils.splitByWholeSeparatorPreserveAllTokens(monthReportSendEmailToUserIds, ";");
            for (String s : appIds) {
                String[] appIdAndUserIds = StringUtils.splitByWholeSeparatorPreserveAllTokens(s, ":");
                Map<String, List<String>> map = new HashMap<>();
                if (StringUtils.isNotBlank(appIdAndUserIds[0])) {
                    map.put("appIds", Arrays.asList(appIdAndUserIds[0].split(",")));
                }
                if (StringUtils.isNotBlank(appIdAndUserIds[1])) {
                    map.put("emailToUserIds", Arrays.asList(appIdAndUserIds[1].split(",")));
                }
                if (StringUtils.isNotBlank(appIdAndUserIds[2])) {
                    map.put("emailCCUserIds", Arrays.asList(appIdAndUserIds[2].split(",")));
                }
                appIdsAndUserIdsList.add(map);
            }
        }
        return appIdsAndUserIdsList;
    }
}
