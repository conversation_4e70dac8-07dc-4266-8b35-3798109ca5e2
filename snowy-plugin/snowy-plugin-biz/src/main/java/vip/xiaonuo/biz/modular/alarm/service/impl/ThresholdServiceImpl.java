package vip.xiaonuo.biz.modular.alarm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.stat.StatUtils;
import org.apache.commons.math3.util.FastMath;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vip.xiaonuo.biz.core.constant.CommonConstant;
import vip.xiaonuo.biz.core.constant.DataSourceConstant;
import vip.xiaonuo.biz.modular.alarm.dto.*;
import vip.xiaonuo.biz.modular.alarm.entity.AlarmNotify;
import vip.xiaonuo.biz.modular.alarm.enums.AlarmConditionValueTypeEnum;
import vip.xiaonuo.biz.modular.alarm.service.AlarmNotifyService;
import vip.xiaonuo.biz.modular.alarm.service.AlarmService;
import vip.xiaonuo.biz.modular.alarm.service.ConditionService;
import vip.xiaonuo.biz.modular.alarm.service.ThresholdService;
import vip.xiaonuo.biz.modular.data.service.MpsService;
import vip.xiaonuo.biz.modular.monitor.dto.MonitorGameDayHistoricalDataDTO;
import vip.xiaonuo.biz.modular.monitor.entity.MonitorGameDay;
import vip.xiaonuo.biz.modular.monitor.service.MonitorGameDayService;
import vip.xiaonuo.biz.modular.syncInfo.entity.DimProductInfo;
import vip.xiaonuo.biz.modular.syncInfo.param.DimProductInfoIdParam;
import vip.xiaonuo.biz.modular.syncInfo.service.DimProductInfoService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 监控阈值计算Service接口实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/2/11 17:55
 */
@Service
@Slf4j
@DS(DataSourceConstant.DYNAMIC_DATASOURCE_MONITOR)
public class ThresholdServiceImpl implements ThresholdService {

    @Autowired
    private AlarmService alarmService;

    @Autowired
    private ConditionService conditionService;

    @Autowired
    private MonitorGameDayService monitorGameDayService;

    @Autowired
    private MpsService mpsService;

    @Autowired
    private DimProductInfoService dimProductInfoService;

    @Autowired
    private AlarmNotifyService alarmNotifyService;

    @Override
    public List<TemplateData> listMonitorDayTemplateDataAllMpid(String day) {
        // 已修正
        List<TemplateData> allTemplateDataList = new ArrayList<>();

        // 数据无效
        Boolean notValidData = Boolean.TRUE;
        Boolean isDayDataAbMps = mpsService.isDayDataAb(day);
        Boolean isDayTagAb = mpsService.isDayTagAb(day);
        // MPS回传数据和标签数据均为正常时该数据才有效
        if ((!isDayDataAbMps) && (!isDayTagAb)) {
            notValidData = Boolean.FALSE;
        }

        // 未修正
        // 统计周期(=1最近1小时,=2最近一天,=3最近一天(覆盖所有场地),=-1最近1小时(数据校验),=-2最近一天(数据校验))
        List<TemplateData> templateDataList = this.listMonitorDayTemplateDataAllMpid(day, "3");
        // 数据修正：1.风控标签数据异常不发送告警；2.对应条件低于最低值不发送告警；
        if (CollectionUtils.isNotEmpty(templateDataList)) {
            for (TemplateData templateData : templateDataList) {
                // 1.风控标签数据异常不发送告警
                templateData.setNotValidData(notValidData);
                // 2.对应条件低于最低值不发送告警
                // 该条告警信息的触发结果
                List<TriggerAlarmResultDTO> allAlarmResultDTOList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(templateData.getTriggerAlarmResultDTOList())) {
                    CollectionUtils.addAll(allAlarmResultDTOList, templateData.getTriggerAlarmResultDTOList());
                }
                if (CollectionUtils.isNotEmpty(templateData.getNotTriggerAlarmResultDTOList())) {
                    CollectionUtils.addAll(allAlarmResultDTOList, templateData.getNotTriggerAlarmResultDTOList());
                }

                // (修正后)该条告警信息的触发结果(触发告警)
                List<TriggerAlarmResultDTO> newTriggerAlarmResultDTOList = new ArrayList<>();
                // (修正后)该条告警信息的触发结果(未触发告警)
                List<TriggerAlarmResultDTO> newNotTriggerAlarmResultDTOList = new ArrayList<>();

                // 修正数据
                if (CollectionUtils.isNotEmpty(allAlarmResultDTOList)) {
                    // 是否触发告警
                    Boolean isAlarm = Boolean.FALSE;
                    for (TriggerAlarmResultDTO triggerAlarmResultDTO : allAlarmResultDTOList) {
                        MonitorGameDay monitorGameDay = triggerAlarmResultDTO.getMonitorGameDay();
                        // 校验ge,用户数低于100不告警
                        if (triggerAlarmResultDTO.getGeThresholdValueAbsolute() != null) {
                            if (monitorGameDay.getUidCnt() == null || Long.valueOf(100).compareTo(monitorGameDay.getUidCnt()) > 0) {
                                triggerAlarmResultDTO.setGeThresholdValueAbsolute(BigDecimal.valueOf(100));
                                triggerAlarmResultDTO.setTriggerResult(Boolean.FALSE);
                            }
                        }
                        if (triggerAlarmResultDTO.getTriggerResult()) {
                            isAlarm = Boolean.TRUE;
                            CollectionUtils.addIgnoreNull(newTriggerAlarmResultDTOList, triggerAlarmResultDTO);
                        } else {
                            CollectionUtils.addIgnoreNull(newNotTriggerAlarmResultDTOList, triggerAlarmResultDTO);
                        }
                    }
                    templateData.setIsAlarm(isAlarm);
                    templateData.setTriggerAlarmResultDTOList(newTriggerAlarmResultDTOList);
                    templateData.setNotTriggerAlarmResultDTOList(newNotTriggerAlarmResultDTOList);
                }

            }
            allTemplateDataList.addAll(templateDataList);
        }

        return allTemplateDataList;
    }

    private List<TemplateData> listMonitorDayTemplateDataAllMpid(String dayHour, String alarmType) {
        Date date;
        String timeStart;
        String timeEnd;
        // 根据统计周期设置时间类型
        // 统计周期(=1最近1小时,=2最近一天,=3最近一天(覆盖所有场地),=-1最近1小时(数据校验),=-2最近一天(数据校验))
        // 时间类型(=0小时,=1天)
        Integer timeType;
        if (StringUtils.equalsIgnoreCase("1", alarmType) || StringUtils.equalsIgnoreCase("-1", alarmType)) {
            timeType = 0;
            date = StringUtils.isBlank(dayHour) ? DateUtil.beginOfHour(DateUtil.offsetHour(new Date(), -1)).toJdkDate() : DateUtil.beginOfHour(DateUtil.parse(dayHour)).toJdkDate();
            timeStart = DateUtil.beginOfHour(date).toString(DatePattern.NORM_DATETIME_PATTERN);
            timeEnd = DateUtil.endOfHour(date).toString(DatePattern.NORM_DATETIME_PATTERN);
        } else if (StringUtils.equalsIgnoreCase("2", alarmType) || StringUtils.equalsIgnoreCase("3", alarmType) || StringUtils.equalsIgnoreCase("-2", alarmType)) {
            timeType = 1;
            date = StringUtils.isBlank(dayHour) ? DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -1)).toJdkDate() : DateUtil.beginOfDay(DateUtil.parse(dayHour)).toJdkDate();
            timeStart = DateUtil.beginOfDay(date).toString(DatePattern.NORM_DATETIME_PATTERN);
            timeEnd = DateUtil.endOfDay(date).toString(DatePattern.NORM_DATETIME_PATTERN);
        } else {
            log.warn("告警统计周期不合法，不执行监控告警,统计周期=[{}](=1最近1小时,=2最近一天,=3最近一天(覆盖所有场地),=-1最近1小时(数据校验),=-2最近一天(数据校验))！", alarmType);
            return null;
        }

        String day = DateUtil.format(date, DatePattern.NORM_DATE_PATTERN);
        Integer hour = DateUtil.hour(date, Boolean.TRUE);

        // 告警信息及告警条件详情
        // 统计周期(=1最近1小时,=2最近一天,=3最近一天(覆盖所有场地),=-1最近1小时(数据校验),=-2最近一天(数据校验))
        List<AlarmDetailDTO> alarmDetailDTOList = alarmService.listAlarmDetail(alarmType, "1", "1", "1");
        if (CollectionUtils.isEmpty(alarmDetailDTOList)) {
            log.warn("告警条件为空，不执行监控告警,统计周期=[{}](=1最近1小时,=2最近一天,=3最近一天(覆盖所有场地),=-1最近1小时(数据校验),=-2最近一天(数据校验))！", alarmType);
            return null;
        }
        // 告警结果
        List<TemplateData> templateDataList = new ArrayList<>();

        // 遍历告警信息及告警条件详情
        for (AlarmDetailDTO alarmDetailDTO : alarmDetailDTOList) {
            // 告警条件
            List<ConditionDetailDTO> conditionDetailDTOList = alarmDetailDTO.getConditionDetailDTOList();
            if (CollectionUtils.isEmpty(conditionDetailDTOList)) {
                log.warn("告警条件为空，不执行监控告警,统计周期=[{}](=1最近1小时,=2最近一天,=3最近一天(覆盖所有场地),=-1最近1小时数据校验)！alarmDetailDTO={}", alarmType, JSONObject.toJSONString(alarmDetailDTO));
                continue;
            }
            List<String> tagNameEnList = new ArrayList<>();
            Map<String, ConditionDetailDTO> conditionDetailDTOMap = new LinkedHashMap<>();
            for (ConditionDetailDTO conditionDetailDTO : conditionDetailDTOList) {
                String conditionContent = StringUtils.defaultIfBlank(conditionDetailDTO.getConditionContent(), "NULL");
                CollectionUtils.addIgnoreNull(tagNameEnList, conditionContent);
                conditionDetailDTOMap.put(conditionContent, conditionDetailDTO);
            }

            // 当前监控值
            Map<Long, List<MonitorGameDay>> monitorGameDayMpIdMapListCurrent = monitorGameDayService.listMonitorGameDayMpIdMap(day, hour, null, timeType, null, 1, tagNameEnList);
            if (MapUtils.isEmpty(monitorGameDayMpIdMapListCurrent)) {
                log.warn("当前待告警的数据为空，不执行监控告警,统计周期=[{}](=1最近1小时,=2最近一天,=3最近一天(覆盖所有场地),=-1最近1小时(数据校验),=-2最近一天(数据校验))！", alarmType);
                continue;
            }
            // 历史数据
            List<MonitorGameDayHistoricalDataDTO> monitorGameDayHistoricalDataDTOListHistorical = monitorGameDayService.listHistoricalDataList(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(day), -7), DatePattern.NORM_DATE_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.parse(day), -1), DatePattern.NORM_DATE_PATTERN), hour, null, timeType, null, 1, tagNameEnList);
            // 按照比赛ID和条件合并历史数据列表
            Map<String, List<Double>> historicalDataListMap = new LinkedHashMap<>();
            if (CollectionUtils.isNotEmpty(monitorGameDayHistoricalDataDTOListHistorical)) {
                for (MonitorGameDayHistoricalDataDTO monitorGameDayHistoricalDataDTO : monitorGameDayHistoricalDataDTOListHistorical) {
                    historicalDataListMap.put(String.format("%s_%s_%s", monitorGameDayHistoricalDataDTO.getMpId(), monitorGameDayHistoricalDataDTO.getDomainNameEn(), monitorGameDayHistoricalDataDTO.getTagNameEn()), monitorGameDayHistoricalDataDTO.getDoubleList());
                }
            }
            // 所有告警通知信息Map(key为gameId)
            Map<Long, AlarmNotify> alarmNotifyMap = alarmNotifyService.getGameIdAlarmNotifyMap();
            // 遍历当前数据所有比赛场地并计算阈值
            for (Long mpId : monitorGameDayMpIdMapListCurrent.keySet()) {
                // 是否触发告警
                Boolean isAlarm = Boolean.FALSE;
                List<MonitorGameDay> monitorGameDayMpIdListCurrent = monitorGameDayMpIdMapListCurrent.getOrDefault(mpId, new ArrayList<>());
                // TODO 统计数据类型和domainNameEn从告警基本信息表中获取
                Integer dataType = 0;
                String domainNameEn = CommonConstant.DEFAULT_DOMAIN_NAME_EN;
                // 遍历该告警的所有告警条件详情
                if (CollectionUtils.isNotEmpty(monitorGameDayMpIdListCurrent)) {
                    // 该条告警信息的触发结果(触发告警)
                    List<TriggerAlarmResultDTO> triggerAlarmResultDTOList = new ArrayList<>();
                    // 该条告警信息的触发结果(未触发告警)
                    List<TriggerAlarmResultDTO> notTriggerAlarmResultDTOList = new ArrayList<>();
                    for (MonitorGameDay monitorGameDay : monitorGameDayMpIdListCurrent) {
                        // TODO 为空要补充空白数据，不存在参赛情况数据也要补充数据
                        if (monitorGameDay == null || monitorGameDay.getUidCnt() == null) {
                            log.warn("无法获取到监控值或监控值为空!可能接收到不支持的条件类型,获取到的数据=[{}]", JSONObject.toJSONString(monitorGameDay));
                            continue;
                        }

                        // 监控的值
                        BigDecimal monitorValue = BigDecimal.valueOf(monitorGameDay.getUidCnt());
                        // 告警条件
                        ConditionDetailDTO conditionDetailDTO = conditionDetailDTOMap.get(monitorGameDay.getTagNameEn());
                        // 历史数据
                        List<Double> historicalDataList = historicalDataListMap.get(String.format("%s_%s_%s", monitorGameDay.getMpId(), monitorGameDay.getDomainNameEn(), monitorGameDay.getTagNameEn()));
                        TriggerAlarmResultDTO triggerAlarmResultDTO = this.isTriggerMonitorHourAlarm(conditionDetailDTO, monitorValue, historicalDataList);
                        triggerAlarmResultDTO.setMonitorGameDay(monitorGameDay);
                        // 是否触发告警
                        if (BooleanUtils.toBooleanDefaultIfNull(triggerAlarmResultDTO.getTriggerResult(), Boolean.FALSE)) {
                            isAlarm = Boolean.TRUE;
                            CollectionUtils.addIgnoreNull(triggerAlarmResultDTOList, triggerAlarmResultDTO);
                        } else {
                            CollectionUtils.addIgnoreNull(notTriggerAlarmResultDTOList, triggerAlarmResultDTO);
                        }
                    }
                    TemplateData templateData = new TemplateData();
                    // TODO 覆盖所有场地:补充数据
                    templateData.setIsAllMpid(Boolean.TRUE);
                    templateData.setIsAlarm(isAlarm);
                    templateData.setDataType(dataType);
                    templateData.setDay(day);
                    templateData.setHour(hour);
                    templateData.setTimeStart(timeStart);
                    templateData.setTimeEnd(timeEnd);
                    // TODO 覆盖所有场地:补充数据(补充比赛产品信息和告警通知信息)
                    AlarmDetailDTO alarmDetailDTOHasDimProductInfoAndAlarmNotify = this.getAlarmDetailDTOHasDimProductInfoAndAlarmNotify(alarmNotifyMap, alarmDetailDTO, mpId, templateData);
                    templateData.setAlarmDetailDTO(alarmDetailDTOHasDimProductInfoAndAlarmNotify);
                    templateData.setTriggerAlarmResultDTOList(triggerAlarmResultDTOList);
                    templateData.setNotTriggerAlarmResultDTOList(notTriggerAlarmResultDTOList);
                    templateData.setCreateTime(DateUtil.formatDateTime(new Date()));
                    CollectionUtils.addIgnoreNull(templateDataList, templateData);
                }
            }
        }
        return templateDataList;
    }

    private TriggerAlarmResultDTO isTriggerMonitorHourAlarm(ConditionDetailDTO conditionDetailDTO, BigDecimal monitorValue, List<Double> historicalDataList) {
        TriggerAlarmResultDTO triggerAlarmResultDTO = new TriggerAlarmResultDTO();
        // 触发的告警条件详情
        triggerAlarmResultDTO.setConditionDetailDTO(conditionDetailDTO);
        // 监控的值绝对值
        triggerAlarmResultDTO.setMonitorValueAbsolute(monitorValue);
        String conditionContent = StringUtils.defaultIfBlank(conditionDetailDTO.getConditionContent(), "NULL");
        // 条件值
        // 大于等于>=
        BigDecimal geValue = conditionDetailDTO.getGeValue();
        // 小于等于<=
        BigDecimal leValue = conditionDetailDTO.getLeValue();
        // 基线，最小值，低于该值触发告警
        BigDecimal minValue = conditionDetailDTO.getMinValue();
        // 基线，最大值，超过该值触发告警
        BigDecimal maxValue = conditionDetailDTO.getMaxValue();
        // 条件值类型
        String valueType = conditionDetailDTO.getValueType();
        // 是否触发告警
        Boolean triggerResult;
        // 1.判断是否触发基线(强制告警)
        triggerResult = (maxValue != null && monitorValue.compareTo(maxValue) > 0) || (minValue != null && monitorValue.compareTo(minValue) < 0);
        triggerAlarmResultDTO.setTriggerResult(triggerResult);
        if (triggerResult) {
            // 是否为强制告警
            triggerAlarmResultDTO.setForceAlarm(Boolean.TRUE);
            // 监控值相对值
            triggerAlarmResultDTO.setMonitorValueRelative(BigDecimal.ZERO);
            triggerAlarmResultDTO.setMonitorValueOldAbsolute(BigDecimal.ZERO);
            // 阈值绝对值
            triggerAlarmResultDTO.setGeThresholdValueAbsolute(maxValue);
            triggerAlarmResultDTO.setLeThresholdValueAbsolute(minValue);
            return triggerAlarmResultDTO;
        }
        // 是否为强制告警
        triggerAlarmResultDTO.setForceAlarm(Boolean.FALSE);

        // 2.判断是否为自定义值类型告警(绝对值)
        // =1自定义值
        if (StringUtils.equalsIgnoreCase(AlarmConditionValueTypeEnum.CUSTOM_VALUE.getValue(), valueType)) {
            triggerResult = (geValue != null && monitorValue.compareTo(geValue) >= 0) || (leValue != null && monitorValue.compareTo(leValue) <= 0);
            triggerAlarmResultDTO.setTriggerResult(triggerResult);
            // 监控值绝对值
            triggerAlarmResultDTO.setMonitorValueRelative(BigDecimal.ZERO);
            triggerAlarmResultDTO.setMonitorValueOldAbsolute(BigDecimal.ZERO);
            // 阈值绝对值
            triggerAlarmResultDTO.setGeThresholdValueAbsolute(geValue);
            triggerAlarmResultDTO.setLeThresholdValueAbsolute(leValue);
            return triggerAlarmResultDTO;
        }

        // 根据历史数据计算相关参数
        // 是否去掉最高和最低
        boolean removeMaxMin = StringUtils.equalsAnyIgnoreCase(valueType, AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_AVG_VALUE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_AVG_RATE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_1_STANDARD_RATE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_2_STANDARD_RATE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_3_STANDARD_RATE.getValue());
        CalculateHistoricalDataDTO calculateHistoricalDataDTO = this.getCalculateHistoricalDataDTO(historicalDataList, removeMaxMin);
        // TODO 不存在历史数据怎么处理？
        if (CollectionUtils.isEmpty(historicalDataList) || CollectionUtils.isEmpty(calculateHistoricalDataDTO.getDoubleList())) {
            log.warn("不存在历史数据，无法根据历史数据计算，默认为不触发告警!条件=[{}]", conditionContent);
            // 不存在最近7日历史数据，无法根据历史数据计算，默认为不触发告警
            triggerResult = Boolean.FALSE;
            triggerAlarmResultDTO.setTriggerResult(triggerResult);
            // 监控值相对值
            triggerAlarmResultDTO.setMonitorValueRelative(BigDecimal.ZERO);
            triggerAlarmResultDTO.setMonitorValueOldAbsolute(BigDecimal.ZERO);
            // 阈值绝对值
            if (geValue != null) {
//                triggerAlarmResultDTO.setGeThresholdValueAbsolute((maxValue != null) ? maxValue : triggerAlarmResultDTO.getMonitorValueAbsolute());
                triggerAlarmResultDTO.setGeThresholdValueAbsolute(triggerAlarmResultDTO.getMonitorValueAbsolute());
            }
            if (leValue != null) {
//                triggerAlarmResultDTO.setLeThresholdValueAbsolute((minValue != null) ? minValue : triggerAlarmResultDTO.getMonitorValueAbsolute());
                triggerAlarmResultDTO.setLeThresholdValueAbsolute(triggerAlarmResultDTO.getMonitorValueAbsolute());
            }
            return triggerAlarmResultDTO;
        }
        // 历史数据
        long size = calculateHistoricalDataDTO.getSize();
        double sum = calculateHistoricalDataDTO.getSum();
        double max = calculateHistoricalDataDTO.getMax();
        double min = calculateHistoricalDataDTO.getMin();
        double average = calculateHistoricalDataDTO.getAverage();
        // 总体方差
        double populationVariance = calculateHistoricalDataDTO.getPopulationVariance();
        // 总体标准差(方差开方)
        double populationStandardDeviation = calculateHistoricalDataDTO.getPopulationStandardDeviation();
        double yesterdayMonitorValue = calculateHistoricalDataDTO.getYesterdayMonitorValue();

        // =2最近7日平均值
        // =3最近7日去掉最高和最低平均值
        if (StringUtils.equalsAnyIgnoreCase(valueType, AlarmConditionValueTypeEnum.DAY_7_DAY_AVG_VALUE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_AVG_VALUE.getValue())) {
            if (geValue != null) {
                geValue = BigDecimal.valueOf(average);
            }
            if (leValue != null) {
                leValue = BigDecimal.valueOf(average);
            }
            triggerResult = (geValue != null && monitorValue.compareTo(geValue) >= 0) || (leValue != null && monitorValue.compareTo(leValue) <= 0);
            triggerAlarmResultDTO.setTriggerResult(triggerResult);
            // 监控值绝对值
            triggerAlarmResultDTO.setMonitorValueRelative(monitorValue);
            triggerAlarmResultDTO.setMonitorValueOldAbsolute(BigDecimal.valueOf(average));
            // 阈值绝对值
            triggerAlarmResultDTO.setGeThresholdValueAbsolute(geValue);
            triggerAlarmResultDTO.setLeThresholdValueAbsolute(leValue);
        } else if (StringUtils.equalsIgnoreCase(AlarmConditionValueTypeEnum.DAY_YESTERDAY_RATE.getValue(), valueType)) {
            // =4相对前一天变化率
            // 相对的旧值绝对值(前一天绝对值)
            BigDecimal monitorValueOldAbsolute = BigDecimal.valueOf(yesterdayMonitorValue);
            if (monitorValueOldAbsolute.compareTo(BigDecimal.ZERO) != 0) {
                // 监控的相对值
                monitorValue = monitorValue.subtract(monitorValueOldAbsolute).divide(monitorValueOldAbsolute, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100.00));

                triggerResult = (geValue != null && monitorValue.compareTo(geValue) >= 0) || (leValue != null && monitorValue.compareTo(leValue) <= 0);
                triggerAlarmResultDTO.setTriggerResult(triggerResult);
                // 监控的相对值
                triggerAlarmResultDTO.setMonitorValueRelative(monitorValue);
                triggerAlarmResultDTO.setMonitorValueOldAbsolute(monitorValueOldAbsolute);
                // 阈值绝对值
                if (geValue != null) {
                    triggerAlarmResultDTO.setGeThresholdValueAbsolute(monitorValueOldAbsolute.multiply(geValue.divide(BigDecimal.valueOf(100.00), 4, BigDecimal.ROUND_HALF_UP).add(BigDecimal.valueOf(1))));
                }
                if (leValue != null) {
                    triggerAlarmResultDTO.setLeThresholdValueAbsolute(monitorValueOldAbsolute.multiply(leValue.divide(BigDecimal.valueOf(100.00), 4, BigDecimal.ROUND_HALF_UP).add(BigDecimal.valueOf(1))));
                }
            }

        } else if (StringUtils.equalsAnyIgnoreCase(valueType, AlarmConditionValueTypeEnum.DAY_7_DAY_AVG_RATE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_AVG_RATE.getValue())) {
            // =5相对最近7日平均值变化率
            // =6相对最近7日去掉最高和最低平均值变化率
            // 相对的旧值绝对值
            BigDecimal monitorValueOldAbsolute = BigDecimal.valueOf(average);
            if (monitorValueOldAbsolute.compareTo(BigDecimal.ZERO) != 0) {
                // 监控的相对值
                monitorValue = monitorValue.subtract(monitorValueOldAbsolute).divide(monitorValueOldAbsolute, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100.00));

                triggerResult = (geValue != null && monitorValue.compareTo(geValue) >= 0) || (leValue != null && monitorValue.compareTo(leValue) <= 0);
                triggerAlarmResultDTO.setTriggerResult(triggerResult);
                // 监控的相对值
                triggerAlarmResultDTO.setMonitorValueRelative(monitorValue);
                triggerAlarmResultDTO.setMonitorValueOldAbsolute(monitorValueOldAbsolute);
                // 阈值绝对值
                if (geValue != null) {
                    triggerAlarmResultDTO.setGeThresholdValueAbsolute(monitorValueOldAbsolute.multiply(geValue.divide(BigDecimal.valueOf(100.00), 4, BigDecimal.ROUND_HALF_UP).add(BigDecimal.valueOf(1))).setScale(0, RoundingMode.HALF_UP));
                }
                if (leValue != null) {
                    triggerAlarmResultDTO.setLeThresholdValueAbsolute(monitorValueOldAbsolute.multiply(leValue.divide(BigDecimal.valueOf(100.00), 4, BigDecimal.ROUND_HALF_UP).add(BigDecimal.valueOf(1))).setScale(0, RoundingMode.HALF_UP));
                }
            }
        } else if (StringUtils.equalsAnyIgnoreCase(valueType, AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_1_STANDARD_RATE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_2_STANDARD_RATE.getValue(), AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_3_STANDARD_RATE.getValue())) {
            // =7相对最近7日去掉最高和最低正态分布1个标准差σ的变化率(正态分布1σ-->68%)
            // =8相对最近7日去掉最高和最低正态分布2个标准差σ的变化率(正态分布2σ-->95%)
            // =9相对最近7日去掉最高和最低正态分布3个标准差σ的变化率(正态分布3σ-->99%)
            long multiply = 3;
            if (StringUtils.equalsIgnoreCase(AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_1_STANDARD_RATE.getValue(), valueType)) {
                multiply = 1;
            } else if (StringUtils.equalsIgnoreCase(AlarmConditionValueTypeEnum.DAY_7_DAY_REMOVE_MAX_MIN_2_STANDARD_RATE.getValue(), valueType)) {
                multiply = 2;
            }
            // 相对的旧值绝对值
            BigDecimal monitorValueOldAbsolute = BigDecimal.valueOf(average).add(BigDecimal.valueOf(populationStandardDeviation).multiply(BigDecimal.valueOf(multiply)));
            if (monitorValueOldAbsolute.compareTo(BigDecimal.ZERO) != 0) {
                // 监控的相对值
                monitorValue = monitorValue.subtract(monitorValueOldAbsolute).divide(monitorValueOldAbsolute, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100.00));

                triggerResult = (geValue != null && monitorValue.compareTo(geValue) >= 0) || (leValue != null && monitorValue.compareTo(leValue) <= 0);
                triggerAlarmResultDTO.setTriggerResult(triggerResult);
                // 监控的相对值
                triggerAlarmResultDTO.setMonitorValueRelative(monitorValue);
                triggerAlarmResultDTO.setMonitorValueOldAbsolute(monitorValueOldAbsolute);
                // 阈值绝对值
                if (geValue != null) {
                    triggerAlarmResultDTO.setGeThresholdValueAbsolute(monitorValueOldAbsolute.multiply(geValue.divide(BigDecimal.valueOf(100.00), 4, BigDecimal.ROUND_HALF_UP).add(BigDecimal.valueOf(1))).setScale(0, RoundingMode.HALF_UP));
                }
                if (leValue != null) {
                    triggerAlarmResultDTO.setLeThresholdValueAbsolute(monitorValueOldAbsolute.multiply(leValue.divide(BigDecimal.valueOf(100.00), 4, BigDecimal.ROUND_HALF_UP).add(BigDecimal.valueOf(1))).setScale(0, RoundingMode.HALF_UP));
                }
            }
        } else {
            log.warn("不支持的阈值类型[{}]", valueType);
        }
        log.debug("判断触发结果[{}],阈值类型[{}],条件=[{}],geValue=[{}],leValue=[{}],监控值=[{}]", JSONObject.toJSONString(triggerAlarmResultDTO), valueType, conditionContent, geValue, leValue, monitorValue);
        return triggerAlarmResultDTO;
    }

    private CalculateHistoricalDataDTO getCalculateHistoricalDataDTO(List<Double> doubleList, boolean removeMaxMin) {
        CalculateHistoricalDataDTO calculateHistoricalDataDTO = new CalculateHistoricalDataDTO();

        // 根据历史数据求平均值等数据
        long size = 0;
        double sum = 0.0;
        double max = 0.0;
        double min = 0.0;
        double average = 0.0;
        // 总体方差
        double populationVariance = 0.0;
        // 总体标准差(方差开方)
        double populationStandardDeviation = 0.0;
        double yesterdayMonitorValue = 0.0;

        if (CollectionUtils.isNotEmpty(doubleList)) {
            // 去掉一个最大值和一个最小值
            List<Double> doubleListNew = new ArrayList<>();
            if (removeMaxMin && CollectionUtils.size(doubleList) > 2) {
                int indexOfMin = doubleList.indexOf(Collections.min(doubleList));
                int indexOfMax = doubleList.indexOf(Collections.max(doubleList));
                for (int i = 0; i < CollectionUtils.size(doubleList); i++) {
                    if (i != indexOfMin && i != indexOfMax) {
                        CollectionUtils.addIgnoreNull(doubleListNew, doubleList.get(i));
                    }
                }
            } else {
                doubleListNew = doubleList;
            }
            if (CollectionUtils.isNotEmpty(doubleListNew)) {
                double[] doubleArray = doubleListNew.stream().mapToDouble(Double::doubleValue).toArray();
                size = doubleArray.length;
                sum = StatUtils.sum(doubleArray);
                max = StatUtils.max(doubleArray);
                min = StatUtils.min(doubleArray);
                average = StatUtils.mean(doubleArray);
                // 总体方差
                populationVariance = StatUtils.populationVariance(doubleArray);
                // 总体标准差(方差开方)
                populationStandardDeviation = FastMath.sqrt(StatUtils.populationVariance(doubleArray));
                calculateHistoricalDataDTO.setDoubleList(doubleListNew);
            }
            if (CollectionUtils.isNotEmpty(doubleList)) {
                yesterdayMonitorValue = doubleList.get(0);
            }
        }
        calculateHistoricalDataDTO.setSize(size);
        calculateHistoricalDataDTO.setSum(sum);
        calculateHistoricalDataDTO.setMax(max);
        calculateHistoricalDataDTO.setMin(min);
        calculateHistoricalDataDTO.setAverage(average);
        calculateHistoricalDataDTO.setPopulationVariance(populationVariance);
        calculateHistoricalDataDTO.setPopulationStandardDeviation(populationStandardDeviation);
        calculateHistoricalDataDTO.setYesterdayMonitorValue(yesterdayMonitorValue);

        return calculateHistoricalDataDTO;
    }

    private AlarmDetailDTO getAlarmDetailDTOHasDimProductInfoAndAlarmNotify(Map<Long, AlarmNotify> alarmNotifyMap, AlarmDetailDTO alarmDetailDTO, Long mpId, TemplateData templateData) {
        AlarmDetailDTO alarmDetailDTOHasDimProductInfoAndAlarmNotify = BeanUtil.toBean(alarmDetailDTO, AlarmDetailDTO.class);
        alarmDetailDTOHasDimProductInfoAndAlarmNotify.setMpId(mpId);

        // 补充 DimProductInfo 数据
        DimProductInfoIdParam dimProductInfoIdParam = new DimProductInfoIdParam();
        dimProductInfoIdParam.setMpId(mpId);
        // 注意：存在根据mpId无法查询到比赛产品信息的情况
        DimProductInfo dimProductInfo = null;
        try {
            dimProductInfo = dimProductInfoService.detail(dimProductInfoIdParam);
        } catch (Exception e) {
            log.warn("获取比赛产品信息失败，mpId=[{}]", mpId, e);
        }
        if (dimProductInfo == null) {
            dimProductInfo = new DimProductInfo();
            dimProductInfo.setMpId(mpId);
//            dimProductInfo.setMpName("未知");
//            dimProductInfo.setGameName("未知");
        }
        alarmDetailDTOHasDimProductInfoAndAlarmNotify.setDimProductInfo(dimProductInfo);
        // TODO 小游戏告警通知信息特殊处理
        if (StringUtils.isNotBlank(dimProductInfo.getMpName()) && StringUtils.containsAnyIgnoreCase(dimProductInfo.getMpName(), "小游戏", "小程序")) {
            List<String> receiveUsersMiniGames = new ArrayList<>(Arrays.asList("25", "5901", "5047", "3802"));
            List<String> ccUsersMiniGames = new ArrayList<>(Arrays.asList("28", "98", "5213", "7572", "5006", "10300", "8325", "4369"));
            String robotTokenMiniGames = "48230602c6ce444f9c8b2d4987c291ad88a87331e3b2f142c1396a663a6fe214";
            String groupIdMiniGames = "groupIdMiniGames";
            if (CollectionUtils.isNotEmpty(receiveUsersMiniGames)) {
                Set<String> receiveUsers = new LinkedHashSet<>(receiveUsersMiniGames);
                if (StringUtils.isNotBlank(alarmDetailDTOHasDimProductInfoAndAlarmNotify.getReceiveUser()) && CollectionUtils.isNotEmpty(JSONObject.parseArray(alarmDetailDTOHasDimProductInfoAndAlarmNotify.getReceiveUser(), String.class))) {
                    receiveUsers.addAll(JSONObject.parseArray(alarmDetailDTOHasDimProductInfoAndAlarmNotify.getReceiveUser(), String.class));
                }
                alarmDetailDTOHasDimProductInfoAndAlarmNotify.setReceiveUser(JSONObject.toJSONString(receiveUsers));
            }
            if (CollectionUtils.isNotEmpty(ccUsersMiniGames)) {
                Set<String> ccUsers = new LinkedHashSet<>(ccUsersMiniGames);
                if (StringUtils.isNotBlank(alarmDetailDTOHasDimProductInfoAndAlarmNotify.getCcUser()) && CollectionUtils.isNotEmpty(JSONObject.parseArray(alarmDetailDTOHasDimProductInfoAndAlarmNotify.getCcUser(), String.class))) {
                    ccUsers.addAll(JSONObject.parseArray(alarmDetailDTOHasDimProductInfoAndAlarmNotify.getCcUser(), String.class));
                }
                alarmDetailDTOHasDimProductInfoAndAlarmNotify.setCcUser(JSONObject.toJSONString(ccUsers));
            }
            if (StringUtils.isNotBlank(robotTokenMiniGames)) {
                // 群机器人消息token
                alarmDetailDTOHasDimProductInfoAndAlarmNotify.setRobotToken(robotTokenMiniGames);
            }
            if (StringUtils.isNotBlank(groupIdMiniGames)) {
                // 通知信息分组ID
                alarmDetailDTOHasDimProductInfoAndAlarmNotify.setGroupId(groupIdMiniGames);
                alarmDetailDTOHasDimProductInfoAndAlarmNotify.setGroupName("小游戏");
            }
            templateData.setIsAllMpid(Boolean.FALSE);
        }
        // 非小游戏按照GameId补充告警通知信息
        // 补充告警通知信息
        else if (dimProductInfo.getGameId() != null) {
            AlarmNotify alarmNotify = alarmNotifyMap.get(dimProductInfo.getGameId());
            if (alarmNotify != null) {
                if (StringUtils.isNotBlank(alarmNotify.getReceiveUser()) && CollectionUtils.isNotEmpty(JSONObject.parseArray(alarmNotify.getReceiveUser(), String.class))) {
                    Set<String> receiveUsers = new LinkedHashSet<>(JSONObject.parseArray(alarmNotify.getReceiveUser(), String.class));
                    if (StringUtils.isNotBlank(alarmDetailDTOHasDimProductInfoAndAlarmNotify.getReceiveUser()) && CollectionUtils.isNotEmpty(JSONObject.parseArray(alarmDetailDTOHasDimProductInfoAndAlarmNotify.getReceiveUser(), String.class))) {
                        receiveUsers.addAll(JSONObject.parseArray(alarmDetailDTOHasDimProductInfoAndAlarmNotify.getReceiveUser(), String.class));
                    }
                    alarmDetailDTOHasDimProductInfoAndAlarmNotify.setReceiveUser(JSONObject.toJSONString(receiveUsers));
                }
                if (StringUtils.isNotBlank(alarmNotify.getCcUser()) && CollectionUtils.isNotEmpty(JSONObject.parseArray(alarmNotify.getCcUser(), String.class))) {
                    Set<String> ccUsers = new LinkedHashSet<>(JSONObject.parseArray(alarmNotify.getCcUser(), String.class));
                    if (StringUtils.isNotBlank(alarmDetailDTOHasDimProductInfoAndAlarmNotify.getCcUser()) && CollectionUtils.isNotEmpty(JSONObject.parseArray(alarmDetailDTOHasDimProductInfoAndAlarmNotify.getCcUser(), String.class))) {
                        ccUsers.addAll(JSONObject.parseArray(alarmDetailDTOHasDimProductInfoAndAlarmNotify.getCcUser(), String.class));
                    }
                    alarmDetailDTOHasDimProductInfoAndAlarmNotify.setCcUser(JSONObject.toJSONString(ccUsers));
                }
                // 群机器人消息token
                alarmDetailDTOHasDimProductInfoAndAlarmNotify.setRobotToken(alarmNotify.getRobotToken());
                // 通知信息分组ID
                alarmDetailDTOHasDimProductInfoAndAlarmNotify.setGroupId(alarmNotify.getGroupId());
                alarmDetailDTOHasDimProductInfoAndAlarmNotify.setGroupName(StringUtils.defaultIfBlank(alarmNotify.getGroupName(), alarmNotify.getGroupId()));
                templateData.setIsAllMpid(Boolean.FALSE);
            }
        }

        return alarmDetailDTOHasDimProductInfoAndAlarmNotify;
    }
}
