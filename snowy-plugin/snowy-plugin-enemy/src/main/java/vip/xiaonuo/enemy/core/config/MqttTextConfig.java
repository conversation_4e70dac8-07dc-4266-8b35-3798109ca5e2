package vip.xiaonuo.enemy.core.config;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.core.MessageProducer;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;

/**
 * MQTT文本识别配置类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/21 20:00
 */
@Configuration
@ConditionalOnProperty(name = "app-config.mqtt.text.enabled", havingValue = "true")
@Slf4j
public class MqttTextConfig {

    @Value("${app-config.mqtt.url:tcp://localhost:1883}")
    private String mqttUrl;

    @Value("${app-config.mqtt.username:}")
    private String mqttUsername;

    @Value("${app-config.mqtt.password:}")
    private String mqttPassword;

    @Value("${app-config.mqtt.text.client-id:snowy-ai-text-client}")
    private String textClientId;

    @Value("${app-config.mqtt.text.request-topic:ai/text/detect/request}")
    private String aiTextRequestTopic;

    @Value("${app-config.mqtt.text.response-topic:ai/text/detect/response}")
    private String aiTextResponseTopic;

    @Value("${app-config.mqtt.text.enabled:false}")
    private boolean mqttTextEnabled;

    /**
     * MQTT文本识别客户端工厂
     */
    @Bean
    public MqttPahoClientFactory mqttTextClientFactory() {
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        MqttConnectOptions options = new MqttConnectOptions();
        options.setServerURIs(new String[]{mqttUrl});
        if (mqttUsername != null && !mqttUsername.isEmpty()) {
            options.setUserName(mqttUsername);
        }
        if (mqttPassword != null && !mqttPassword.isEmpty()) {
            options.setPassword(mqttPassword.toCharArray());
        }
        options.setCleanSession(false);
        options.setConnectionTimeout(30);
        options.setKeepAliveInterval(60);
        options.setAutomaticReconnect(true);
        factory.setConnectionOptions(options);
        return factory;
    }

    /**
     * MQTT文本识别出站通道
     */
    @Bean
    public MessageChannel mqttTextOutboundChannel() {
        return new DirectChannel();
    }

    /**
     * MQTT文本识别入站通道
     */
    @Bean
    public MessageChannel mqttTextInputChannel() {
        return new DirectChannel();
    }

    /**
     * MQTT文本识别出站消息处理器
     */
    @Bean
    @ServiceActivator(inputChannel = "mqttTextOutboundChannel")
    public MessageHandler mqttTextOutbound() {
        MqttPahoMessageHandler messageHandler = new MqttPahoMessageHandler(textClientId + "_pub", mqttTextClientFactory());
        messageHandler.setAsync(true);
        messageHandler.setDefaultTopic(aiTextRequestTopic);
        messageHandler.setDefaultRetained(false);
        messageHandler.setDefaultQos(2);
        return messageHandler;
    }

    /**
     * MQTT文本识别入站消息适配器
     */
    @Bean
    public MessageProducer mqttTextInbound() {
        MqttPahoMessageDrivenChannelAdapter adapter = new MqttPahoMessageDrivenChannelAdapter(
                textClientId + "_sub", mqttTextClientFactory(), aiTextResponseTopic);
        adapter.setCompletionTimeout(5000);
        adapter.setConverter(new DefaultPahoMessageConverter());
        adapter.setQos(2);
        adapter.setOutputChannel(mqttTextInputChannel());
        return adapter;
    }

    public String getAiTextRequestTopic() {
        return aiTextRequestTopic;
    }

    public String getAiTextResponseTopic() {
        return aiTextResponseTopic;
    }

    public boolean isMqttTextEnabled() {
        return mqttTextEnabled;
    }
}
