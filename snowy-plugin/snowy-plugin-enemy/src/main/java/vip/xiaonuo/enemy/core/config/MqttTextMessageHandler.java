package vip.xiaonuo.enemy.core.config;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import vip.xiaonuo.enemy.modular.enemy.dto.AiTextDetectResponseDTO;
import vip.xiaonuo.enemy.modular.enemy.service.MqttAiTextDetectService;

/**
 * MQTT文本识别消息处理器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/21 21:00
 */
@Component
@ConditionalOnProperty(name = "app-config.mqtt.text.enabled", havingValue = "true")
@Slf4j
public class MqttTextMessageHandler {

    @Autowired
    private MqttAiTextDetectService mqttAiTextDetectService;

    @ServiceActivator(inputChannel = "mqttTextInputChannel")
    public void handleMqttTextMessage(Message<String> message) {
        try {
            String payload = message.getPayload();
            log.info("收到AI文本识别响应：{}", payload);
            
            AiTextDetectResponseDTO responseDTO = JSONObject.parseObject(payload, AiTextDetectResponseDTO.class);
            mqttAiTextDetectService.handleAiTextDetectResponse(responseDTO);
        } catch (Exception e) {
            log.error("处理AI文本识别响应失败", e);
        }
    }
}
