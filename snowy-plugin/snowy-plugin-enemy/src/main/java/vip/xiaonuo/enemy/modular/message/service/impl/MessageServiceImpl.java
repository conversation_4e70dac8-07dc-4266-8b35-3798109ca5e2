package vip.xiaonuo.enemy.modular.message.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import feign.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import vip.xiaonuo.enemy.core.config.EnemyAppConfig;
import vip.xiaonuo.enemy.modular.freemarker.service.FreemarkerService;
import vip.xiaonuo.enemy.modular.message.constant.MessageConstant;
import vip.xiaonuo.enemy.modular.message.dto.SendEmailDTO;
import vip.xiaonuo.enemy.modular.message.dto.SendRobotDTO;
import vip.xiaonuo.enemy.modular.message.dto.SendXingeDTO;
import vip.xiaonuo.enemy.modular.message.service.MessageService;
import vip.xiaonuo.enemy.modular.remote.dto.TokenEsbDTO;
import vip.xiaonuo.enemy.modular.remote.dto.TokenEsbRequestParamDTO;
import vip.xiaonuo.enemy.modular.remote.service.EsbRemoteService;
import vip.xiaonuo.enemy.modular.remote.service.OpenApiRemoteService;

import java.util.*;

/**
 * Description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/3/1 16:27
 */
@Service(value = "enemyMessageService")
@Slf4j
public class MessageServiceImpl implements MessageService {

    @Autowired
    private EsbRemoteService esbRemoteService;

    @Autowired
    private FreemarkerService freemarkerService;

    @Autowired
    private OpenApiRemoteService openApiRemoteService;

//    @Autowired
//    private CacheManager cacheManager;

    @Override
    public TokenEsbDTO defaultTokenEsb(Boolean refreshToken) {
        // TODO 补充缓存功能
        TokenEsbDTO tokenEsbDTO = null;
//        refreshToken = BooleanUtils.toBooleanDefaultIfNull(refreshToken, Boolean.FALSE);

        // 如果不刷新则直接获取缓存
//        if (!refreshToken) {
//            Cache cache = cacheManager.getCache(CachingConstant.DEFAULT_TOKEN_ESB_CACHE_CACHE_NAME);
//            tokenEsbDTO = cache.get(CachingConstant.DEFAULT_TOKEN_ESB_CACHE_CACHE_KEY, TokenEsbDTO.class);
//            log.debug("从缓存中获取token结果tokenEsbDTO=[{}]", JSONObject.toJSONString(tokenEsbDTO));
//            if (tokenEsbDTO != null) {
//                return tokenEsbDTO;
//            }
//        }

        // 刷新token
        TokenEsbRequestParamDTO tokenEsbRequestParamDTO = new TokenEsbRequestParamDTO();
        tokenEsbRequestParamDTO.setAccount(EnemyAppConfig.ESB_ACCOUNT);
        tokenEsbRequestParamDTO.setPassword(EnemyAppConfig.ESB_PASSWORD);
        try {
            tokenEsbDTO = esbRemoteService.tokenEsb(tokenEsbRequestParamDTO);
            log.debug("刷新token成功，tokenEsbDTO=[{}]", JSONObject.toJSONString(tokenEsbDTO));
        } catch (Exception e) {
            log.error("使用默认账号密码获取esbToken失败!", e);
        }

        // 更新缓存
//        Cache cache = cacheManager.getCache(CachingConstant.DEFAULT_TOKEN_ESB_CACHE_CACHE_NAME);
//        cache.put(CachingConstant.DEFAULT_TOKEN_ESB_CACHE_CACHE_KEY, tokenEsbDTO);

        return tokenEsbDTO;
    }

    @Override
    public boolean sendXingeV2(SendXingeDTO sendXingeDTO) {
        if (!EnemyAppConfig.ESB_XINGE_ENABLED || sendXingeDTO == null || StringUtils.isBlank(sendXingeDTO.getBodyNotBase64()) || CollectionUtils.isEmpty(sendXingeDTO.getReceivers())) {
            log.warn("信鸽通知处于关闭状态或消息为空，将无法发送信鸽通知!sendXingeDTO={}", JSONObject.toJSONString(sendXingeDTO));
            return Boolean.FALSE;
        }
        // 使用默认账号密码获取esbToken
        TokenEsbDTO tokenEsbDTO = this.defaultTokenEsb(null);
        sendXingeDTO.setSource(String.valueOf(tokenEsbDTO.getAppId()));
        Response response = esbRemoteService.sendXingeV2(sendXingeDTO, tokenEsbDTO.getToken());

        // 远程返回401，token失效，强制刷新token并重试
        if (response.status() == HttpStatus.SC_UNAUTHORIZED) {
            tokenEsbDTO = this.defaultTokenEsb(Boolean.TRUE);
            sendXingeDTO.setSource(String.valueOf(tokenEsbDTO.getAppId()));
            log.warn(String.format("第一次发送信鸽消息失败，原因token失效,将强制刷新token并重试，第一次发送信鸽消息返回结果status=[%s],headers=[%s],body=[%s],String=[%s],强制刷新token后tokenEsbDTO=[%s]", response.status(), JSONObject.toJSONString(response.headers()), JSONObject.toJSONString(response.body()), JSONObject.toJSONString(response.toString()), JSONObject.toJSONString(tokenEsbDTO)));
            response = esbRemoteService.sendXingeV2(sendXingeDTO, tokenEsbDTO.getToken());
        }

        // 发送结果，远程返回200表示发送成功
        Boolean result = response.status() == HttpStatus.SC_OK ? Boolean.TRUE : Boolean.FALSE;
        if (result) {
            log.info(String.format("发送信鸽消息成功!接收人列表receivers=%s,返回结果status=[%s]", JSONObject.toJSONString(sendXingeDTO.getReceivers()), response.status()));
        } else {
            log.warn(String.format("发送信鸽消息失败!接收人列表receivers=%s,sendXingeDTO=[%s],返回结果status=[%s],headers=[%s],body=[%s],String=[%s]", JSONObject.toJSONString(sendXingeDTO.getReceivers()), JSONObject.toJSONString(sendXingeDTO), response.status(), JSONObject.toJSONString(response.headers()), JSONObject.toJSONString(response.body()), JSONObject.toJSONString(response.toString())));
        }

        return result;
    }

    @Override
    public boolean sendEmail(SendEmailDTO sendEmailDTO) {
        if (!EnemyAppConfig.ESB_EMAIL_ENABLED || sendEmailDTO == null || StringUtils.isBlank(sendEmailDTO.getBodyNotBase64()) || CollectionUtils.isEmpty(sendEmailDTO.getEmailTo())) {
            log.warn("邮件通知处于关闭状态或消息为空，将无法发送邮件通知!sendEmailDTO={}", JSONObject.toJSONString(sendEmailDTO));
            return Boolean.FALSE;
        }
        // 使用默认账号密码获取esbToken
        TokenEsbDTO tokenEsbDTO = this.defaultTokenEsb(null);
        sendEmailDTO.setSource(String.valueOf(tokenEsbDTO.getAppId()));
        // 发送邮件需要对整个 msgjson 的内容做一下 urlencode，因此需要使用MultiValueMap，并且设置为application/x-www-form-urlencoded
        String msgJson = JSONObject.toJSONString(sendEmailDTO);
        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        multiValueMap.set("msgJson", msgJson);
        Response response = esbRemoteService.sendEmail(multiValueMap, tokenEsbDTO.getToken());

        // 远程返回401，token失效，强制刷新token并重试
        if (response.status() == HttpStatus.SC_UNAUTHORIZED) {
            tokenEsbDTO = this.defaultTokenEsb(Boolean.TRUE);
            log.warn(String.format("第一次发送邮件失败，原因token失效,将强制刷新token并重试，第一次发送邮件返回结果status=[%s],headers=[%s],body=[%s],String=[%s],强制刷新token后tokenEsbDTO=[%s]", response.status(), JSONObject.toJSONString(response.headers()), JSONObject.toJSONString(response.body()), JSONObject.toJSONString(response.toString()), JSONObject.toJSONString(tokenEsbDTO)));
            response = esbRemoteService.sendEmail(multiValueMap, tokenEsbDTO.getToken());
        }

        // 发送结果，远程返回200表示发送成功
        Boolean result = response.status() == HttpStatus.SC_OK ? Boolean.TRUE : Boolean.FALSE;
        if (result) {
            log.info(String.format("发送邮件成功!接收人列表emailTo=%s,抄送人列表emailCC=%s,返回结果status=[%s]", JSONObject.toJSONString(sendEmailDTO.getEmailTo()), JSONObject.toJSONString(sendEmailDTO.getEmailCC()), response.status()));
        } else {
            log.warn(String.format("发送邮件失败!接收人列表emailTo=%s,抄送人列表emailCC=%s,sendEmailDTO=[%s],返回结果status=[%s],headers=[%s],body=[%s],String=[%s]", JSONObject.toJSONString(sendEmailDTO.getEmailTo()), JSONObject.toJSONString(sendEmailDTO.getEmailCC()), JSONObject.toJSONString(sendEmailDTO), response.status(), JSONObject.toJSONString(response.headers()), JSONObject.toJSONString(response.body()), JSONObject.toJSONString(response.toString())));
        }
        return Boolean.TRUE;
    }

    @Override
    public boolean sendDDRobot(SendRobotDTO sendRobotDTO) {
        if (StringUtils.isBlank(EnemyAppConfig.OPEN_API_URL) || sendRobotDTO == null || StringUtils.isBlank(sendRobotDTO.getToken()) || (StringUtils.isBlank(sendRobotDTO.getText()) && StringUtils.isBlank(sendRobotDTO.getMarkdown()))) {
            log.warn("接口开放平台(OpenApi)接口处于关闭状态或消息为空，将无法发送机器人群消息!sendRobotDTO={}", JSONObject.toJSONString(sendRobotDTO));
            return Boolean.FALSE;
        }
        Boolean success = Boolean.FALSE;
        try {
            JSONObject result = openApiRemoteService.sendDDRobot(sendRobotDTO, EnemyAppConfig.OPEN_API_ACCOUNT, EnemyAppConfig.OPEN_API_PASSWORD, sendRobotDTO.getToken());
            success = result.getBoolean("success");
            if (success) {
                log.info(String.format("发送机器人群消息成功!access_token=[%s],返回结果=[%s]", sendRobotDTO.getToken(), result.getString("message")));
            } else {
                String errorCode = result.getString("code");
                String errorMessage = result.getString("message");

                // 检查是否是频率限制错误，出现频率限制重试一次
                // {"code":"660026","success":false,"message":"sending too many messages per minute"}
                if (StringUtils.equalsIgnoreCase(errorCode, "660026") || StringUtils.containsIgnoreCase(errorMessage, "sending too many messages per minute")) {
                    log.warn(String.format("钉钉机器人发送频率受限，等待1分钟后重试。access_token=[%s]", sendRobotDTO.getToken()));

                    try {
                        // 等待1分钟
                        Thread.sleep(60 * 1000);

                        // 重试发送
                        log.info(String.format("开始重试发送钉钉机器人消息。access_token=[%s]", sendRobotDTO.getToken()));
                        JSONObject retryResult = openApiRemoteService.sendDDRobot(sendRobotDTO, EnemyAppConfig.OPEN_API_ACCOUNT, EnemyAppConfig.OPEN_API_PASSWORD, sendRobotDTO.getToken());
                        success = retryResult.getBoolean("success");

                        if (success) {
                            log.info(String.format("重试发送机器人群消息成功!access_token=[%s],返回结果=[%s]", sendRobotDTO.getToken(), retryResult.getString("message")));
                        } else {
                            log.error(String.format("重试发送机器人群消息仍然失败!access_token=[%s],sendRobotDTO=[%s],返回结果=[%s]", sendRobotDTO.getToken(), JSONObject.toJSONString(sendRobotDTO), JSONObject.toJSONString(retryResult)));
                        }
                    } catch (InterruptedException e) {
                        log.error("等待重试过程中被中断", e);
                        Thread.currentThread().interrupt();
                    }
                } else {
                    log.warn(String.format("发送机器人群消息失败!access_token=[%s],sendRobotDTO=[%s],返回结果=[%s]", sendRobotDTO.getToken(), JSONObject.toJSONString(sendRobotDTO), JSONObject.toJSONString(result)));
                }
            }
        } catch (Exception e) {
            log.error(String.format("发送机器人群消息远程接口调用失败!access_token=[%s],sendRobotDTO=[%s]", sendRobotDTO.getToken(), JSONObject.toJSONString(sendRobotDTO)), e);
        }

        return success;
    }

    @Override
    public boolean sendDDRobot(List<SendRobotDTO> sendRobotDTOList) {
        if (CollectionUtils.isNotEmpty(sendRobotDTOList)) {
            for (SendRobotDTO sendRobotDTO : sendRobotDTOList) {
                this.sendDDRobot(sendRobotDTO);
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public boolean sendXingeV2(List<SendXingeDTO> sendXingeDTOList) {
        if (CollectionUtils.isNotEmpty(sendXingeDTOList)) {
            for (SendXingeDTO sendXingeDTO : sendXingeDTOList) {
                this.sendXingeV2(sendXingeDTO);
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public boolean sendEmail(List<SendEmailDTO> sendEmailDTOList) {
        if (CollectionUtils.isNotEmpty(sendEmailDTOList)) {
            for (SendEmailDTO sendEmailDTO : sendEmailDTOList) {
                this.sendEmail(sendEmailDTO);
            }
        }
        return Boolean.TRUE;
    }

//    @Override
//    public SendXingeDTO generateSendXingeMessage(String sendXingeMessageTemplate, TemplateData templateData) {
//        if (templateData == null) {
//            return null;
//        }
//        if (StringUtils.isBlank(sendXingeMessageTemplate)) {
//            sendXingeMessageTemplate = MessageConstant.SEND_XINGE_MONITOR_MESSAGE_TEMPLATE;
//        }
//        // 根据模板文件和模板数据生成消息字符串
//        String bodyNotBase64 = freemarkerService.generateStringByTemplateFile(sendXingeMessageTemplate, templateData);
//        if (StringUtils.isBlank(bodyNotBase64)) {
//            log.warn("根据模板文件和模板数据生成消息字符串为空!发送信鸽消息模板=[{}],sendXingeMessageTemplateData=[{}]", sendXingeMessageTemplate, JSONObject.toJSONString(templateData));
//        }
//        SendXingeDTO sendXingeDTO = new SendXingeDTO();
//        sendXingeDTO.setRecType(1);
//        sendXingeDTO.setSubject("JJ Bugly 告警");
//        sendXingeDTO.setBodyNotBase64(bodyNotBase64);
//        // 合并接收人和抄送人
//        List<String> receivers = new ArrayList<>();
////        if (CollectionUtils.isNotEmpty(templateData.getAlarmDetailDTO().getReceiveUsersWarn())) {
////            for (Integer userId : templateData.getAlarmDetailDTO().getReceiveUsersWarn()) {
////                CollectionUtils.addIgnoreNull(receivers, String.valueOf(userId));
////            }
////        }
////        if (CollectionUtils.isNotEmpty(templateData.getAlarmDetailDTO().getCcUsersWarn())) {
////            for (Integer userId : templateData.getAlarmDetailDTO().getCcUsersWarn()) {
////                CollectionUtils.addIgnoreNull(receivers, String.valueOf(userId));
////            }
////        }
//        sendXingeDTO.setReceivers(receivers);
//        return sendXingeDTO;
//    }

//    @Override
//    public SendEmailDTO generateSendEmailMessage(String sendEmailMessageTemplate, TemplateData templateData) {
//        if (templateData == null) {
//            return null;
//        }
//        if (StringUtils.isBlank(sendEmailMessageTemplate)) {
//            sendEmailMessageTemplate = MessageConstant.SEND_EMAIL_MONITOR_MESSAGE_TEMPLATE;
//        }
//        // 根据模板文件和模板数据生成消息字符串
//        String bodyNotBase64 = freemarkerService.generateStringByTemplateFile(sendEmailMessageTemplate, templateData);
//        if (StringUtils.isBlank(bodyNotBase64)) {
//            log.warn("根据模板文件和模板数据生成消息字符串为空!发送邮件消息模板=[{}],sendXingeMessageTemplateData=[{}]", sendEmailMessageTemplate, JSONObject.toJSONString(templateData));
//        }
//        SendEmailDTO sendEmailDTO = new SendEmailDTO();
//        // 邮件内容是否是 html 格式，0-普通文本，1-html
//        sendEmailDTO.setIsBodyHtml(1);
//        // 接收者类型，0-接收人/抄送人列表中是邮件地址，1-接收人/抄送人列表中是用户ID
//        sendEmailDTO.setRecType(1);
//        sendEmailDTO.setSubject("JJ Bugly 告警");
//        sendEmailDTO.setBodyNotBase64(bodyNotBase64);
//        // 接收人和抄送人
//        if (CollectionUtils.isNotEmpty(templateData.getAlarmDetailDTO().getReceiveUsersWarn())) {
//            List<String> emailTo = new ArrayList<>();
//            for (Integer userId : templateData.getAlarmDetailDTO().getReceiveUsersWarn()) {
//                CollectionUtils.addIgnoreNull(emailTo, String.valueOf(userId));
//            }
//            sendEmailDTO.setEmailTo(emailTo);
//        }
//        if (CollectionUtils.isNotEmpty(templateData.getAlarmDetailDTO().getCcUsersWarn())) {
//            List<String> emailCC = new ArrayList<>();
//            for (Integer userId : templateData.getAlarmDetailDTO().getCcUsersWarn()) {
//                CollectionUtils.addIgnoreNull(emailCC, String.valueOf(userId));
//            }
//            sendEmailDTO.setEmailCC(emailCC);
//        }
//        return sendEmailDTO;
//    }

    @Override
    public SendEmailDTO generateSendEmailMessage(String sendEmailMessageTemplate, Object templateData, String subject, List<String> emailTo, List<String> emailCC) {
        if (templateData == null) {
            return null;
        }
        if (StringUtils.isBlank(sendEmailMessageTemplate)) {
            sendEmailMessageTemplate = MessageConstant.SEND_EMAIL_ENEMY_MESSAGE_AI_DETECT_TEMPLATE;
        }
        // 根据模板文件和模板数据生成消息字符串
        String bodyNotBase64 = freemarkerService.generateStringByTemplateFile(sendEmailMessageTemplate, templateData);
        if (StringUtils.isBlank(bodyNotBase64)) {
            log.warn("根据模板文件和模板数据生成消息字符串为空!发送邮件消息模板=[{}],templateData=[{}]", sendEmailMessageTemplate, JSONObject.toJSONString(templateData));
        }
        SendEmailDTO sendEmailDTO = new SendEmailDTO();
        // 邮件内容是否是 html 格式，0-普通文本，1-html
        sendEmailDTO.setIsBodyHtml(1);
        // 接收者类型，0-接收人/抄送人列表中是邮件地址，1-接收人/抄送人列表中是用户ID
        sendEmailDTO.setRecType(1);
        sendEmailDTO.setSubject(subject);
        sendEmailDTO.setBodyNotBase64(bodyNotBase64);
        sendEmailDTO.setEmailTo(emailTo);
        if (CollectionUtils.isNotEmpty(emailCC)) {
            sendEmailDTO.setEmailCC(emailCC);
        }
        return sendEmailDTO;
    }

    @Override
    public SendRobotDTO generateSendDDRobotMessage(String sendDDRobotMessageTemplate, Object templateData, String msgtype, String accessToken) {
        if (templateData == null) {
            return null;
        }
        if (StringUtils.isBlank(sendDDRobotMessageTemplate)) {
            sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_TEMPLATE;
        }

        if (StringUtils.isBlank(msgtype)) {
            msgtype = "text";
        }

        if (StringUtils.isBlank(accessToken)) {
            accessToken = EnemyAppConfig.ROBOT_MESSAGE_TOKEN_OF_DEFAULT;
        }

        // 根据模板文件和模板数据生成消息字符串
        String bodyNotBase64 = freemarkerService.generateStringByTemplateFile(sendDDRobotMessageTemplate, templateData);
        if (StringUtils.isBlank(bodyNotBase64)) {
            log.warn("根据模板文件和模板数据生成消息字符串为空!发送机器人群消息模板=[{}],sendXingeMessageTemplateData=[{}]", sendDDRobotMessageTemplate, JSONObject.toJSONString(templateData));
        }

        SendRobotDTO sendRobotDTO = new SendRobotDTO();
        sendRobotDTO.setToken(accessToken);
        if (StringUtils.equalsIgnoreCase(msgtype, "text")) {
            sendRobotDTO.setMsgtype(msgtype);
            Map<String, String> text = new HashMap<>();
            text.put("content", bodyNotBase64);
            sendRobotDTO.setText(JSONObject.toJSONString(text));
        } else {
            sendRobotDTO.setMsgtype(msgtype);
            Map<String, String> markdown = new HashMap<>();
            markdown.put("title", "消息通知");
            markdown.put("text", bodyNotBase64);
            sendRobotDTO.setMarkdown(JSONObject.toJSONString(markdown));
        }

        return sendRobotDTO;
    }

    @Override
    public boolean sendXingeAdministratorMessage(String msg, List<String> receiveUsers, List<String> ccUsers) {
        List<String> receiversAll = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(receiveUsers)) {
            CollectionUtils.addAll(receiversAll, receiveUsers);
        }
        if (CollectionUtils.isNotEmpty(ccUsers)) {
            CollectionUtils.addAll(receiversAll, ccUsers);
        }
        if (CollectionUtils.isNotEmpty(EnemyAppConfig.SYSTEM_ADMINISTRATOR_USERIDS)) {
            CollectionUtils.addAll(receiversAll, EnemyAppConfig.SYSTEM_ADMINISTRATOR_USERIDS);
        }
        SendXingeDTO sendXingeDTO = new SendXingeDTO();
        sendXingeDTO.setBodyNotBase64("JJ Neural 敌情线索：\r\n尊敬的用户，您好，【" + msg + "】，请留意!\r\n此消息发送时间：" + DateUtil.formatDateTime(new Date()));
        sendXingeDTO.setReceivers(receiversAll);
        sendXingeDTO.setSubject("JJ Neural 告警");
        sendXingeDTO.setRecType(1);
        this.sendXingeV2(sendXingeDTO);
        return Boolean.TRUE;
    }

    @Override
    public JSONObject getBasicInfoByUserId(Long userId) {
        if (StringUtils.isBlank(EnemyAppConfig.OPEN_API_URL) || userId == null) {
            log.warn("接口开放平台(OpenApi)接口处于关闭状态或查询的用户ID为空，将无法执行用户信息查询!userId={}", userId);
            return null;
        }
        try {
            JSONObject params = new JSONObject();
            params.put("UserID", userId);
            JSONObject result = openApiRemoteService.getBasicInfoByParams(params, EnemyAppConfig.OPEN_API_ACCOUNT, EnemyAppConfig.OPEN_API_PASSWORD);
            Boolean success = (result != null && userId.equals(result.getLong("UserID")));
            if (success) {
                return result;
            } else {
                log.warn(String.format("根据UserId获取用户信息失败!userId=[%s],返回结果=[%s]", userId, JSONObject.toJSONString(result)));
            }
        } catch (Exception e) {
            log.error(String.format("根据UserId获取用户信息远程接口调用失败!userId=[%s]", userId), e);
        }

        return null;
    }
}
