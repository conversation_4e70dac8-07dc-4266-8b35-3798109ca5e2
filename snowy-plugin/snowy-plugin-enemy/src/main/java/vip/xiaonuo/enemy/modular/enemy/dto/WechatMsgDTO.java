package vip.xiaonuo.enemy.modular.enemy.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 微信群消息传输对象
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/31 14:49
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "微信群消息传输对象")
public class WechatMsgDTO implements Serializable {

    private static final Long serialVersionUID = 1L;
    /*
{
  "data": {
    "msg": "拉宾是以色列杀的吗",
    "user_id": "wxid_ccf9wgmht74k22",
    "displayname": "",
    "nickname": "战忽包子",
    "msg_time": "2024-11-17 11:54:50"
  },
  "group_name": "技术交流群 SFW 不许键政",
  "channel": "wechat",
  "users_cnt": 140
}
     */

    @ApiModelProperty(value = "渠道，wechat 微信群", name = "channel", required = true)
    private String channel;

    @ApiModelProperty(value = "群名称", name = "groupName", required = true)
    @JSONField(name = "group_name")
    private String groupName;

    @ApiModelProperty(value = "用户数", name = "usersCnt", required = true)
    @JSONField(name = "users_cnt")
    private Long usersCnt;

    @ApiModelProperty(value = "数据", name = "data", required = true)
    private WechatMsgDataDTO data;

}
