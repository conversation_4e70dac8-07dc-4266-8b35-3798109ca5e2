/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.enemy.modular.kw.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.enemy.modular.kw.entity.EnemyKw;
import vip.xiaonuo.enemy.modular.kw.param.EnemyKwAddParam;
import vip.xiaonuo.enemy.modular.kw.param.EnemyKwEditParam;
import vip.xiaonuo.enemy.modular.kw.param.EnemyKwIdParam;
import vip.xiaonuo.enemy.modular.kw.param.EnemyKwPageParam;

import java.util.List;
import java.util.Set;

/**
 * 关键字管理Service接口
 *
 * <AUTHOR>
 * @date 2025/01/02 16:48
 **/
public interface EnemyKwService extends IService<EnemyKw> {

    /**
     * 获取关键字管理分页
     *
     * <AUTHOR>
     * @date 2025/01/02 16:48
     */
    Page<EnemyKw> page(EnemyKwPageParam enemyKwPageParam);

    /**
     * 获取关键字管理列表
     *
     * <AUTHOR>
     * @date 2025/01/02 16:48
     */
    List<EnemyKw> list(EnemyKwPageParam enemyKwPageParam);

    /**
     * 获取关键字Set
     *
     * <AUTHOR>
     * @date 2025/01/02 16:48
     */
    Set<String> listKwSet(EnemyKwPageParam enemyKwPageParam);

    /**
     * 添加关键字管理
     *
     * <AUTHOR>
     * @date 2025/01/02 16:48
     */
    void add(EnemyKwAddParam enemyKwAddParam);

    /**
     * 编辑关键字管理
     *
     * <AUTHOR>
     * @date 2025/01/02 16:48
     */
    void edit(EnemyKwEditParam enemyKwEditParam);

    /**
     * 删除关键字管理
     *
     * <AUTHOR>
     * @date 2025/01/02 16:48
     */
    void delete(List<EnemyKwIdParam> enemyKwIdParamList);

    /**
     * 获取关键字管理详情
     *
     * <AUTHOR>
     * @date 2025/01/02 16:48
     */
    EnemyKw detail(EnemyKwIdParam enemyKwIdParam);

    /**
     * 获取关键字管理详情
     *
     * <AUTHOR>
     * @date 2025/01/02 16:48
     **/
    EnemyKw queryEntity(String id);
}
