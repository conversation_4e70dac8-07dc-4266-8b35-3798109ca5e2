package vip.xiaonuo.enemy.modular.enemy.dto;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 线索消息传输对象
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/31 15:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "线索消息传输对象")
public class XiansuoMsgDTO implements Serializable {

    private static final Long serialVersionUID = 1L;
    /*
{
"item_info":  {
"price": 15.38 ,
"name": "🔥iOS曙光英雄【黑豹】天卡/全图内透/免签名" ,
"stock": "28" ,
"tid": "46782" ,
"sales": "0" ,
"cid": "677" ,
"desc": "iOS官方网盘：https://ios888.icu<br /> iOS官方网盘2：https://zs777.icu<br /> <br />"
},
"domain": "lilingyu1" ,
"channel": "xiansuo" ,
"keyword": "曙光" ,
"url": "https://li.lilingyu1.cn/?v=1753259583620"
}
     */

    @ApiModelProperty(value = "渠道，xiansuo 线索", name = "channel", required = true)
    private String channel;

    @ApiModelProperty(value = "触发关键词", name = "keyword", required = false)
    private String keyword;

    @ApiModelProperty(value = "域名", name = "domain", required = true)
    private String domain;

    @ApiModelProperty(value = "链接", name = "url", required = true)
    private String url;

    @ApiModelProperty(value = "商品信息（动态字段）", name = "itemInfo", required = true)
    @JSONField(name = "item_info")
    private JSONObject itemInfo;

    @ApiModelProperty(value = "创建时间", name = "createTime")
    @JSONField(name = "create_time")
    private String createTime;
}
