/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.enemy.modular.kw.controller;

import cn.dev33.satoken.annotation.SaCheckBasic;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.enemy.modular.kw.entity.EnemyKw;
import vip.xiaonuo.enemy.modular.kw.param.EnemyKwAddParam;
import vip.xiaonuo.enemy.modular.kw.param.EnemyKwEditParam;
import vip.xiaonuo.enemy.modular.kw.param.EnemyKwIdParam;
import vip.xiaonuo.enemy.modular.kw.param.EnemyKwPageParam;
import vip.xiaonuo.enemy.modular.kw.service.EnemyKwService;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Set;

/**
 * 关键字管理控制器
 *
 * <AUTHOR>
 * @date 2025/01/02 16:48
 */
@Api(tags = "关键字管理控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@RequestMapping("enemy/kw/enemyKw")
@Validated
@Slf4j
public class EnemyKwController {

    @Resource
    private EnemyKwService enemyKwService;

    /**
     * 获取关键字管理分页
     *
     * <AUTHOR>
     * @date 2025/01/02 16:48
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取关键字管理分页")
    @GetMapping("page")
    public CommonResult<Page<EnemyKw>> page(EnemyKwPageParam enemyKwPageParam) {
        return CommonResult.data(enemyKwService.page(enemyKwPageParam));
    }

    /**
     * 获取关键字管理列表
     *
     * <AUTHOR>
     * @date 2025/01/02 16:48
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取关键字管理分页")
    @GetMapping("list")
    public CommonResult<List<EnemyKw>> list(EnemyKwPageParam enemyKwPageParam) {
        return CommonResult.data(enemyKwService.list(enemyKwPageParam));
    }

    /**
     * 添加关键字管理
     *
     * <AUTHOR>
     * @date 2025/01/02 16:48
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("添加关键字管理")
    @CommonLog("添加关键字管理")
    @PostMapping("add")
    public CommonResult<String> add(@RequestBody @Valid EnemyKwAddParam enemyKwAddParam) {
        enemyKwService.add(enemyKwAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑关键字管理
     *
     * <AUTHOR>
     * @date 2025/01/02 16:48
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("编辑关键字管理")
    @CommonLog("编辑关键字管理")
    @PostMapping("edit")
    public CommonResult<String> edit(@RequestBody @Valid EnemyKwEditParam enemyKwEditParam) {
        enemyKwService.edit(enemyKwEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除关键字管理
     *
     * <AUTHOR>
     * @date 2025/01/02 16:48
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("删除关键字管理")
    @CommonLog("删除关键字管理")
    @PostMapping("delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                       CommonValidList<EnemyKwIdParam> enemyKwIdParamList) {
        enemyKwService.delete(enemyKwIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取关键字管理详情
     *
     * <AUTHOR>
     * @date 2025/01/02 16:48
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("获取关键字管理详情")
    @GetMapping("detail")
    public CommonResult<EnemyKw> detail(@Valid EnemyKwIdParam enemyKwIdParam) {
        return CommonResult.data(enemyKwService.detail(enemyKwIdParam));
    }

    /**
     * 获取关键字管理分页
     *
     * <AUTHOR>
     * @date 2025/01/02 16:48
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取关键字管理列表(贴吧名称)")
    @CommonLog("获取关键字管理列表(贴吧名称)")
    @SaCheckBasic
    @GetMapping("listTiebaName")
    public CommonResult<Set<String>> listTiebaName() {
        EnemyKwPageParam enemyKwPageParam = new EnemyKwPageParam();
        // 关键字类型，=tieba_hit 贴吧触发，=tieba_name 贴吧名称，=goofish_search 闲鱼搜索
        enemyKwPageParam.setKwType("tieba_name");
        // 状态，-1删除，0无效，1有效
        enemyKwPageParam.setStatus("1");
        return CommonResult.data(enemyKwService.listKwSet(enemyKwPageParam));
    }

    @ApiOperationSupport(order = 1)
    @ApiOperation("获取关键字管理列表(贴吧触发关键词)")
    @CommonLog("获取关键字管理列表(贴吧触发关键词)")
    @SaCheckBasic
    @GetMapping("listTiebaHit")
    public CommonResult<Set<String>> listTiebaHit() {
        EnemyKwPageParam enemyKwPageParam = new EnemyKwPageParam();
        // 关键字类型，=tieba_hit 贴吧触发，=tieba_name 贴吧名称，=goofish_search 闲鱼搜索
        enemyKwPageParam.setKwType("tieba_hit");
        // 状态，-1删除，0无效，1有效
        enemyKwPageParam.setStatus("1");
        return CommonResult.data(enemyKwService.listKwSet(enemyKwPageParam));
    }

    @ApiOperationSupport(order = 1)
    @ApiOperation("获取关键字管理列表(闲鱼搜索关键字)")
    @CommonLog("获取关键字管理列表(闲鱼搜索关键字)")
    @SaCheckBasic
    @GetMapping("listGoofishSearch")
    public CommonResult<Set<String>> listGoofishSearch() {
        EnemyKwPageParam enemyKwPageParam = new EnemyKwPageParam();
        // 关键字类型，=tieba_hit 贴吧触发，=tieba_name 贴吧名称，=goofish_search 闲鱼搜索
        enemyKwPageParam.setKwType("goofish_search");
        // 状态，-1删除，0无效，1有效
        enemyKwPageParam.setStatus("1");
        return CommonResult.data(enemyKwService.listKwSet(enemyKwPageParam));
    }
}
