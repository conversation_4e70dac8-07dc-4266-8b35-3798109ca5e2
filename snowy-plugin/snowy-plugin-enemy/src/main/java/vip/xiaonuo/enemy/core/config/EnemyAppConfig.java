package vip.xiaonuo.enemy.core.config;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/12 17:38
 */
@Slf4j
@Configuration
public class EnemyAppConfig {
    // ------------------------------------------------------------------------
    //  ESB相关配置
    // ------------------------------------------------------------------------
    /**
     * ESB服务器地址
     */
    public static String ESB_URL;

    /**
     * ESB账号
     */
    public static String ESB_ACCOUNT;

    /**
     * ESB密码
     */
    public static String ESB_PASSWORD;

    /**
     * OpenApi服务器地址
     */
    public static String OPEN_API_URL;

    /**
     * OpenApi账号
     */
    public static String OPEN_API_ACCOUNT;

    /**
     * OpenApi密码
     */
    public static String OPEN_API_PASSWORD;

    /**
     * 开启信鸽通知
     */
    public static Boolean ESB_XINGE_ENABLED;

    /**
     * 开启Email通知
     */
    public static Boolean ESB_EMAIL_ENABLED;

    /**
     * # ECharts Export Server
     */
    public static String ECHARTS_EXPORT_SERVER_URL;

    /**
     * 系统管理员用户ID(用于接收系统消息等)
     */
    public static List<String> SYSTEM_ADMINISTRATOR_USERIDS;
    /**
     * MPS管理员用户ID(用于接收MPS数据异常等消息)
     */
    public static List<String> MPS_ADMINISTRATOR_USERIDS;
    /**
     * DPS管理员用户ID(用于接收DPS数据异常等消息)
     */
    public static List<String> DPS_ADMINISTRATOR_USERIDS;
    /**
     * tag管理员用户ID(用于接收风控标签数据异常等消息)
     */
    public static List<String> TAG_ADMINISTRATOR_USERIDS;

    /**
     * bugly管理员用户ID(用于bugly数据异常等消息)
     */
    public static List<String> BUGLY_ADMINISTRATOR_USERIDS;

    /**
     * 策略模板python命令
     */
    public static String POLICY_TEMPLATE_PYTHON_CMD;
    /**
     * 策略模板文件py
     */
    public static String POLICY_TEMPLATE_FILE_PATH_PY;
    /**
     * 策略模板文件目录(83文件和json文件)
     */
    public static String POLICY_TEMPLATE_FILE_PATH_OUT;

    /**
     * 策略模板管理token
     */
    public static String POLICY_TEMPLATE_MANAGE_TOKEN;

    /**
     * 文件下载 baseUrl
     */
    public static String POLICY_DOWNLOAD_OSS_BASE_URL;

    /**
     * 群机器人消息默认token
     */
    public static String ROBOT_MESSAGE_TOKEN_OF_DEFAULT;

    /**
     * 群机器人消息游戏token
     */
    public static Map<Long, String> ROBOT_MESSAGE_TOKEN_OF_GAME_ID;

    /**
     * 群机器人消息标签英文名对应token
     */
    public static Map<String, String> ROBOT_MESSAGE_TOKEN_OF_TAG;

    @Value("${app-config.esb.url}")
    public void setEsbUrl(String esbUrl) {
        ESB_URL = esbUrl;
    }

    @Value("${app-config.esb.account}")
    public void setEsbAccount(String esbAccount) {
        ESB_ACCOUNT = esbAccount;
    }

    @Value("${app-config.esb.password}")
    public void setEsbPassword(String esbPassword) {
        ESB_PASSWORD = esbPassword;
    }

    @Value("${app-settings.esb.xinge.enabled}")
    public void setEsbXingeEnabled(Boolean esbXingeEnabled) {
        ESB_XINGE_ENABLED = esbXingeEnabled == null ? Boolean.TRUE : esbXingeEnabled;
    }

    @Value("${app-settings.esb.email.enabled}")
    public void setEsbEmailEnabled(Boolean esbEmailEnabled) {
        ESB_EMAIL_ENABLED = esbEmailEnabled == null ? Boolean.TRUE : esbEmailEnabled;
    }

    @Value("${app-config.echarts-export-server.url}")
    public void setEchartsExportServerUrl(String echartsExportServerUrl) {
        ECHARTS_EXPORT_SERVER_URL = echartsExportServerUrl;
    }

    @Value("${app-settings.system.administrator.userids}")
    public void setSystemAdministratorUserids(String systemAdministratorUserids) {
        String toUserIds = StringUtils.deleteWhitespace(systemAdministratorUserids);
        if (StringUtils.isNotBlank(toUserIds)) {
            SYSTEM_ADMINISTRATOR_USERIDS = Arrays.asList(toUserIds.split(","));
        }
    }

    @Value("${app-settings.mps.administrator.userids}")
    public void setMpsAdministratorUserids(String mpsAdministratorUserids) {
        String toUserIds = StringUtils.deleteWhitespace(mpsAdministratorUserids);
        if (StringUtils.isNotBlank(toUserIds)) {
            MPS_ADMINISTRATOR_USERIDS = Arrays.asList(toUserIds.split(","));
        }
    }

    @Value("${app-settings.dps.administrator.userids}")
    public void setDpsAdministratorUserids(String dpsAdministratorUserids) {
        String toUserIds = StringUtils.deleteWhitespace(dpsAdministratorUserids);
        if (StringUtils.isNotBlank(toUserIds)) {
            DPS_ADMINISTRATOR_USERIDS = Arrays.asList(toUserIds.split(","));
        }
    }

    @Value("${app-settings.tag.administrator.userids}")
    public void setTagAdministratorUserids(String tagAdministratorUserids) {
        String toUserIds = StringUtils.deleteWhitespace(tagAdministratorUserids);
        if (StringUtils.isNotBlank(toUserIds)) {
            TAG_ADMINISTRATOR_USERIDS = Arrays.asList(toUserIds.split(","));
        }
    }

    @Value("${app-settings.bugly.administrator.userids}")
    public void setBuglyAdministratorUserids(String buglyAdministratorUserids) {
        String toUserIds = StringUtils.deleteWhitespace(buglyAdministratorUserids);
        if (StringUtils.isNotBlank(toUserIds)) {
            BUGLY_ADMINISTRATOR_USERIDS = Arrays.asList(toUserIds.split(","));
        }
    }

    @Value("${app-config.policy.template.python.cmd}")
    public void setPolicyTemplatePythonCmd(String policyTemplatePythonCmd) {
        POLICY_TEMPLATE_PYTHON_CMD = policyTemplatePythonCmd;
    }

    @Value("${app-config.policy.template.file.path.py}")
    public void setPolicyTemplateFilePathPy(String policyTemplateFilePathPy) {
        POLICY_TEMPLATE_FILE_PATH_PY = policyTemplateFilePathPy;
    }

    @Value("${app-config.policy.template.file.path.out}")
    public void setPolicyTemplateFilePathOut(String policyTemplateFilePathOut) {
        POLICY_TEMPLATE_FILE_PATH_OUT = policyTemplateFilePathOut;
    }

    @Value("${app-config.policy.template.manage.token}")
    public void setPolicyTemplateManageToken(String policyTemplateManageToken) {
        POLICY_TEMPLATE_MANAGE_TOKEN = policyTemplateManageToken;
    }

    @Value("${app-config.policy.download.oss.base-url}")
    public void setPolicyDownloadOssBaseUrl(String policyDownloadOssBaseUrl) {
        POLICY_DOWNLOAD_OSS_BASE_URL = policyDownloadOssBaseUrl;
    }

    @Value("${app-config.open-api.url}")
    public void setOpenApiUrl(String openApiUrl) {
        OPEN_API_URL = openApiUrl;
    }

    @Value("${app-config.open-api.account}")
    public void setOpenApiAccount(String openApiAccount) {
        OPEN_API_ACCOUNT = openApiAccount;
    }

    @Value("${app-config.open-api.password}")
    public void setOpenApiPassword(String openApiPassword) {
        OPEN_API_PASSWORD = openApiPassword;
    }

    @Value("${app-config.robot.message.token-of-default}")
    public void setRobotMessageTokenOfDefault(String robotMessageTokenOfDefault) {
        ROBOT_MESSAGE_TOKEN_OF_DEFAULT = robotMessageTokenOfDefault;
    }

    @Value("${app-config.robot.message.token-of-game-id}")
    public void setRobotMessageTokenOfGameId(String robotMessageTokenOfGameId) {
        ROBOT_MESSAGE_TOKEN_OF_GAME_ID = new LinkedHashMap<>();
        robotMessageTokenOfGameId = StringUtils.deleteWhitespace(robotMessageTokenOfGameId);
        String[] robotMessageTokenOfGameIdStrArr = StringUtils.splitByWholeSeparatorPreserveAllTokens(robotMessageTokenOfGameId, ";");
        if (ArrayUtils.isNotEmpty(robotMessageTokenOfGameIdStrArr)) {
            for (String robotMessageTokenOfGameIdStr : robotMessageTokenOfGameIdStrArr) {
                String[] gameIdAndToken = StringUtils.splitByWholeSeparatorPreserveAllTokens(robotMessageTokenOfGameIdStr, ":");
                if (ArrayUtils.isNotEmpty(gameIdAndToken) && ArrayUtils.getLength(gameIdAndToken) > 1 && StringUtils.isNotBlank(gameIdAndToken[1])) {
                    String token = gameIdAndToken[1];
                    String[] gameIdStrArr = StringUtils.splitByWholeSeparatorPreserveAllTokens(gameIdAndToken[0], ",");
                    if (ArrayUtils.isNotEmpty(gameIdStrArr)) {
                        for (String gameIdStr : gameIdStrArr) {
                            // 注意：游戏ID只能为数字
                            if (StringUtils.isNumeric(gameIdStr)) {
                                try {
                                    Long gameId = Long.valueOf(gameIdStr);
                                    ROBOT_MESSAGE_TOKEN_OF_GAME_ID.put(gameId, token);
                                } catch (NumberFormatException e) {
                                    log.warn("解析游戏ID异常，gameIdStr={}", gameIdStr, e);
                                }
                            }
                        }
                    }
                }
            }
        }
        log.info("机器人群消息游戏ID对应token解析结果=[{}]", JSONObject.toJSONString(ROBOT_MESSAGE_TOKEN_OF_GAME_ID));
    }

    @Value("${app-config.robot.message.token-of-tag}")
    public void setRobotMessageTokenOfTag(String robotMessageTokenOfTag) {
        ROBOT_MESSAGE_TOKEN_OF_TAG = new LinkedHashMap<>();
        robotMessageTokenOfTag = StringUtils.deleteWhitespace(robotMessageTokenOfTag);
        String[] robotMessageTokenOfTagStrArr = StringUtils.splitByWholeSeparatorPreserveAllTokens(robotMessageTokenOfTag, ";");
        if (ArrayUtils.isNotEmpty(robotMessageTokenOfTagStrArr)) {
            for (String robotMessageTokenOfTagStr : robotMessageTokenOfTagStrArr) {
                String[] tagAndToken = StringUtils.splitByWholeSeparatorPreserveAllTokens(robotMessageTokenOfTagStr, ":");
                if (ArrayUtils.isNotEmpty(tagAndToken) && ArrayUtils.getLength(tagAndToken) > 1 && StringUtils.isNotBlank(tagAndToken[1])) {
                    String token = tagAndToken[1];
                    String[] tagArr = StringUtils.splitByWholeSeparatorPreserveAllTokens(tagAndToken[0], ",");
                    if (ArrayUtils.isNotEmpty(tagArr)) {
                        for (String tag : tagArr) {
                            ROBOT_MESSAGE_TOKEN_OF_TAG.put(tag, token);
                        }
                    }
                }
            }
        }
        log.info("机器人群消息标签英文名对应token解析结果=[{}]", JSONObject.toJSONString(ROBOT_MESSAGE_TOKEN_OF_TAG));
    }
}
