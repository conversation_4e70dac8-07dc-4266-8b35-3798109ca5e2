/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.enemy.modular.enemy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.enemy.core.config.MqttTextConfig;
import vip.xiaonuo.enemy.modular.echarts.service.EChartsService;
import vip.xiaonuo.enemy.modular.echarts.utils.EchartsJavaUtils;
import vip.xiaonuo.enemy.modular.enemy.dto.*;
import vip.xiaonuo.enemy.modular.remote.dto.EChartsExportParamDTO;
import org.icepear.echarts.Line;
import org.icepear.echarts.charts.line.LineSeries;
import org.icepear.echarts.render.Engine;
import vip.xiaonuo.enemy.modular.enemy.entity.EnemyMsg;
import vip.xiaonuo.enemy.modular.enemy.mapper.EnemyMsgMapper;
import vip.xiaonuo.enemy.modular.enemy.param.*;
import vip.xiaonuo.enemy.core.constant.DataSourceConstant;
import vip.xiaonuo.enemy.modular.enemy.service.AIService;
import vip.xiaonuo.enemy.modular.enemy.service.EnemyMsgService;
import vip.xiaonuo.enemy.modular.enemy.service.MqttAiTextDetectService;
import vip.xiaonuo.enemy.modular.message.constant.MessageConstant;
import vip.xiaonuo.enemy.modular.message.dto.SendEmailDTO;
import vip.xiaonuo.enemy.modular.message.dto.SendRobotDTO;
import vip.xiaonuo.enemy.modular.message.service.MessageService;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 敌情线索-线索表Service接口实现类
 *
 * <AUTHOR>
 * @date 2024/12/13 13:34
 **/
@Service
@DS(DataSourceConstant.DYNAMIC_DATASOURCE_ENEMY_MESSAGE)
@Slf4j
public class EnemyMsgServiceImpl extends ServiceImpl<EnemyMsgMapper, EnemyMsg> implements EnemyMsgService {

    @Autowired
    private AIService aiService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private EChartsService eChartsService;

    @Autowired
    private MqttAiTextDetectService mqttAiTextDetectService;

    @Value("${app-config.robot.enemy.msg.token}")
    private String enemyMsgToken;

    @Value("${app-config.enemy.msg.email.to}")
    private String enemyMsgEmailTo;

    @Value("${app-config.enemy.msg.email.cc}")
    private String enemyMsgEmailCc;

    @Value("${app-config.robot.enemy.msg.tokenOfShuguang}")
    private String enemyMsgTokenShuguang;

    @Autowired(required = false)
    private MqttTextConfig mqttTextConfig;

    @Override
    public Page<EnemyMsg> page(EnemyMsgPageParam enemyMsgPageParam) {
        QueryWrapper<EnemyMsg> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(enemyMsgPageParam.getSourceType())) {
            queryWrapper.lambda().eq(EnemyMsg::getSourceType, enemyMsgPageParam.getSourceType());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgPageParam.getHitKeyword())) {
            queryWrapper.lambda().like(EnemyMsg::getHitKeyword, enemyMsgPageParam.getHitKeyword());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgPageParam.getContentName())) {
            queryWrapper.lambda().like(EnemyMsg::getContentName, enemyMsgPageParam.getContentName());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgPageParam.getContentId())) {
            queryWrapper.lambda().like(EnemyMsg::getContentId, enemyMsgPageParam.getContentId());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgPageParam.getContentUser())) {
            queryWrapper.lambda().like(EnemyMsg::getContentUser, enemyMsgPageParam.getContentUser());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgPageParam.getContentDesc())) {
            queryWrapper.lambda().like(EnemyMsg::getContentDesc, enemyMsgPageParam.getContentDesc());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgPageParam.getDataJson())) {
            queryWrapper.lambda().like(EnemyMsg::getDataJson, enemyMsgPageParam.getDataJson());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgPageParam.getContentStatus())) {
            queryWrapper.lambda().eq(EnemyMsg::getContentStatus, enemyMsgPageParam.getContentStatus());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgPageParam.getAbResult())) {
            queryWrapper.lambda().like(EnemyMsg::getAbResult, "\"" + enemyMsgPageParam.getAbResult() + "\"");
        }
        if (ObjectUtil.isAllNotEmpty(enemyMsgPageParam.getSortField(), enemyMsgPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(enemyMsgPageParam.getSortOrder());
            queryWrapper.orderBy(true, enemyMsgPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(enemyMsgPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(EnemyMsg::getCreateTime).orderByDesc(EnemyMsg::getUpdateTime).orderByDesc(EnemyMsg::getContentTime);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Override
    public List<EnemyMsg> list(EnemyMsgListParam enemyMsgListParam) {
        QueryWrapper<EnemyMsg> queryWrapper = new QueryWrapper<>();

        if (ObjectUtil.isNotEmpty(enemyMsgListParam.getSourceType())) {
            queryWrapper.lambda().eq(EnemyMsg::getSourceType, enemyMsgListParam.getSourceType());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListParam.getHitKeyword())) {
            queryWrapper.lambda().like(EnemyMsg::getHitKeyword, enemyMsgListParam.getHitKeyword());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListParam.getContentName())) {
            queryWrapper.lambda().like(EnemyMsg::getContentName, enemyMsgListParam.getContentName());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListParam.getContentId())) {
            queryWrapper.lambda().like(EnemyMsg::getContentId, enemyMsgListParam.getContentId());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListParam.getContentUser())) {
            queryWrapper.lambda().like(EnemyMsg::getContentUser, enemyMsgListParam.getContentUser());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListParam.getContentDesc())) {
            queryWrapper.lambda().like(EnemyMsg::getContentDesc, enemyMsgListParam.getContentDesc());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListParam.getDataJson())) {
            queryWrapper.lambda().like(EnemyMsg::getDataJson, enemyMsgListParam.getDataJson());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListParam.getContentStatus())) {
            queryWrapper.lambda().eq(EnemyMsg::getContentStatus, enemyMsgListParam.getContentStatus());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListParam.getAbResult())) {
            queryWrapper.lambda().like(EnemyMsg::getAbResult, "\"" + enemyMsgListParam.getAbResult() + "\"");
        }

        // 状态筛选
        if (ObjectUtil.isNotEmpty(enemyMsgListParam.getStatus())) {
            queryWrapper.lambda().eq(EnemyMsg::getStatus, enemyMsgListParam.getStatus());
        }

        // AI相关筛选条件
        if (ObjectUtil.isNotEmpty(enemyMsgListParam.getAiDistinctStatus())) {
            queryWrapper.lambda().eq(EnemyMsg::getAiDistinctStatus, enemyMsgListParam.getAiDistinctStatus());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListParam.getAiDistinctContent())) {
            queryWrapper.lambda().eq(EnemyMsg::getAiDistinctContent, enemyMsgListParam.getAiDistinctContent());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListParam.getAiDistinctResult())) {
            queryWrapper.lambda().eq(EnemyMsg::getAiDistinctResult, enemyMsgListParam.getAiDistinctResult());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListParam.getAiDetectStatus())) {
            queryWrapper.lambda().eq(EnemyMsg::getAiDetectStatus, enemyMsgListParam.getAiDetectStatus());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListParam.getAiDetectResult())) {
            queryWrapper.lambda().like(EnemyMsg::getAiDetectResult, "\"" + enemyMsgListParam.getAiDetectResult() + "\"");
        }


        // 日期范围筛选（创建时间）- 使用DATE函数进行日期比较
        String startDate = StringUtils.isBlank(enemyMsgListParam.getStartDate()) ? DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -7)).toDateStr() : DateUtil.beginOfDay(DateUtil.parse(enemyMsgListParam.getStartDate())).toDateStr();
        String endDate = StringUtils.isBlank(enemyMsgListParam.getEndDate()) ? DateUtil.endOfDay(DateUtil.date()).toDateStr() : DateUtil.endOfDay(DateUtil.parse(enemyMsgListParam.getEndDate())).toDateStr();
//        if (StringUtils.isNotBlank(enemyMsgListParam.getStartDate())) {
//            queryWrapper.apply("DATE(create_time) >= {0}", enemyMsgListParam.getStartDate());
//        }
        queryWrapper.apply("DATE(create_time) >= {0}", startDate);
//        if (StringUtils.isNotBlank(enemyMsgListParam.getEndDate())) {
//            queryWrapper.apply("DATE(create_time) <= {0}", enemyMsgListParam.getEndDate());
//        }
        queryWrapper.apply("DATE(create_time) <= {0}", endDate);

        // 日期范围筛选（内容时间）- 使用DATE函数进行日期比较
        if (StringUtils.isNotBlank(enemyMsgListParam.getStartContentDate())) {
            queryWrapper.apply("DATE(content_time) >= {0}", DateUtil.parse(enemyMsgListParam.getStartContentDate()).toDateStr());
        }
        if (StringUtils.isNotBlank(enemyMsgListParam.getEndContentDate())) {
            queryWrapper.apply("DATE(content_time) <= {0}", DateUtil.parse(enemyMsgListParam.getEndContentDate()).toDateStr());
        }

        // 排序处理
        if (ObjectUtil.isAllNotEmpty(enemyMsgListParam.getSortField(), enemyMsgListParam.getSortOrder())) {
            CommonSortOrderEnum.validate(enemyMsgListParam.getSortOrder());
            queryWrapper.orderBy(true, enemyMsgListParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(enemyMsgListParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(EnemyMsg::getCreateTime).orderByDesc(EnemyMsg::getUpdateTime).orderByDesc(EnemyMsg::getContentTime);
        }

        return this.list(queryWrapper);
    }

    @Override
    public void add(EnemyMsgAddParam enemyMsgAddParam) {
        EnemyMsg enemyMsg = BeanUtil.toBean(enemyMsgAddParam, EnemyMsg.class);
        if (StringUtils.isBlank(enemyMsg.getStatus())) {
            // 状态，-1删除，0无效，1有效
            enemyMsg.setStatus("1");
        }

        if (StringUtils.isBlank(enemyMsg.getAiDistinctStatus())) {
            // AI去重状态，-1删除(无效)，0待处理，1已处理
            enemyMsg.setAiDistinctStatus("0");
        }

        if (StringUtils.isBlank(enemyMsg.getAiDetectStatus())) {
            // AI检测状态，-1删除(无效)，0待处理，1已处理
            enemyMsg.setAiDetectStatus("0");
        }
        this.save(enemyMsg);

        // 异步更新识别结果（线索类型不需要AI识别）
        if (StringUtils.isNotBlank(enemyMsg.getContentName()) && !StringUtils.equalsIgnoreCase(enemyMsg.getSourceType(), "xiansuo")) {
            // 优先使用MQTT方式进行AI文本识别
            if (mqttTextConfig != null && mqttTextConfig.isMqttTextEnabled()) {
                mqttAiTextDetectService.sendAiTextDetectRequest(enemyMsg.getMsgId(), enemyMsg.getContentName());
            } else {
                // 如果MQTT方式关闭，则使用原有的直接接口调用方式
                aiService.setAiDetectResult(enemyMsg.getMsgId(), enemyMsg.getContentName());
            }
        }
    }

    @Override
    public void edit(EnemyMsgEditParam enemyMsgEditParam) {
        EnemyMsg enemyMsg = this.queryEntity(enemyMsgEditParam.getMsgId());
        BeanUtil.copyProperties(enemyMsgEditParam, enemyMsg);
        this.updateById(enemyMsg);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<EnemyMsgIdParam> enemyMsgIdParamList) {
        // 执行删除
        this.removeBatchByIds(CollStreamUtil.toList(enemyMsgIdParamList, EnemyMsgIdParam::getMsgId));
    }

    @Override
    public EnemyMsg detail(EnemyMsgIdParam enemyMsgIdParam) {
        return this.queryEntity(enemyMsgIdParam.getMsgId());
    }

    @Override
    public List<EnemyMsg> listAiDistinct(EnemyMsgListAiParam enemyMsgListAiParam) {
        QueryWrapper<EnemyMsg> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getSourceType())) {
            queryWrapper.lambda().eq(EnemyMsg::getSourceType, enemyMsgListAiParam.getSourceType());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getHitKeyword())) {
            queryWrapper.lambda().like(EnemyMsg::getHitKeyword, enemyMsgListAiParam.getHitKeyword());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getContentName())) {
            queryWrapper.lambda().like(EnemyMsg::getContentName, enemyMsgListAiParam.getContentName());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getContentId())) {
            queryWrapper.lambda().like(EnemyMsg::getContentId, enemyMsgListAiParam.getContentId());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getContentUser())) {
            queryWrapper.lambda().like(EnemyMsg::getContentUser, enemyMsgListAiParam.getContentUser());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getContentDesc())) {
            queryWrapper.lambda().like(EnemyMsg::getContentDesc, enemyMsgListAiParam.getContentDesc());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getDataJson())) {
            queryWrapper.lambda().like(EnemyMsg::getDataJson, enemyMsgListAiParam.getDataJson());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getContentStatus())) {
            queryWrapper.lambda().eq(EnemyMsg::getContentStatus, enemyMsgListAiParam.getContentStatus());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getAbResult())) {
            queryWrapper.lambda().like(EnemyMsg::getAbResult, "\"" + enemyMsgListAiParam.getAbResult() + "\"");
        }

        // AI去重状态
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getAiDistinctStatus())) {
            queryWrapper.lambda().eq(EnemyMsg::getAiDistinctStatus, enemyMsgListAiParam.getAiDistinctStatus());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getAiDistinctResult())) {
            queryWrapper.lambda().eq(EnemyMsg::getAiDistinctResult, enemyMsgListAiParam.getAiDistinctResult());
        }

        // AI检测状态
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getAiDetectStatus())) {
            queryWrapper.lambda().eq(EnemyMsg::getAiDetectStatus, enemyMsgListAiParam.getAiDetectStatus());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getAiDetectResult())) {
            queryWrapper.lambda().like(EnemyMsg::getAiDetectResult, "\"" + enemyMsgListAiParam.getAiDetectResult() + "\"");
        }

        // 是否历史数据筛选
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getInHistory())) {
            queryWrapper.lambda().eq(EnemyMsg::getInHistory, enemyMsgListAiParam.getInHistory());
        }

        queryWrapper.lambda().orderByDesc(EnemyMsg::getCreateTime).orderByDesc(EnemyMsg::getUpdateTime).orderByDesc(EnemyMsg::getContentTime);
        List<EnemyMsg> enemyMsgList = this.list(queryWrapper);

        // 更新AI去重状态
        if (CollectionUtils.isNotEmpty(enemyMsgList)) {
            String setAiDistinctStatus = enemyMsgListAiParam.getSetAiDistinctStatus();
            if (StringUtils.isNotBlank(setAiDistinctStatus)) {
                // 更新AI去重状态
                EnemyMsg enemyMsgUpdate = new EnemyMsg();
                enemyMsgUpdate.setAiDistinctStatus(setAiDistinctStatus);
                this.update(enemyMsgUpdate, queryWrapper);
            }
        }

        return enemyMsgList;
    }

    @Override
    public List<EnemyMsg> listAiDetect(EnemyMsgListAiParam enemyMsgListAiParam) {
        QueryWrapper<EnemyMsg> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getSourceType())) {
            queryWrapper.lambda().eq(EnemyMsg::getSourceType, enemyMsgListAiParam.getSourceType());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getHitKeyword())) {
            queryWrapper.lambda().like(EnemyMsg::getHitKeyword, enemyMsgListAiParam.getHitKeyword());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getContentName())) {
            queryWrapper.lambda().like(EnemyMsg::getContentName, enemyMsgListAiParam.getContentName());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getContentId())) {
            queryWrapper.lambda().like(EnemyMsg::getContentId, enemyMsgListAiParam.getContentId());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getContentUser())) {
            queryWrapper.lambda().like(EnemyMsg::getContentUser, enemyMsgListAiParam.getContentUser());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getContentDesc())) {
            queryWrapper.lambda().like(EnemyMsg::getContentDesc, enemyMsgListAiParam.getContentDesc());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getDataJson())) {
            queryWrapper.lambda().like(EnemyMsg::getDataJson, enemyMsgListAiParam.getDataJson());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getContentStatus())) {
            queryWrapper.lambda().eq(EnemyMsg::getContentStatus, enemyMsgListAiParam.getContentStatus());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getAbResult())) {
            queryWrapper.lambda().like(EnemyMsg::getAbResult, "\"" + enemyMsgListAiParam.getAbResult() + "\"");
        }

        // AI去重状态
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getAiDistinctStatus())) {
            queryWrapper.lambda().eq(EnemyMsg::getAiDistinctStatus, enemyMsgListAiParam.getAiDistinctStatus());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getAiDistinctResult())) {
            queryWrapper.lambda().eq(EnemyMsg::getAiDistinctResult, enemyMsgListAiParam.getAiDistinctResult());
        }

        // AI检测状态
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getAiDetectStatus())) {
            queryWrapper.lambda().eq(EnemyMsg::getAiDetectStatus, enemyMsgListAiParam.getAiDetectStatus());
        }
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getAiDetectResult())) {
            queryWrapper.lambda().like(EnemyMsg::getAiDetectResult, "\"" + enemyMsgListAiParam.getAiDetectResult() + "\"");
        }

        // 是否历史数据筛选
        if (ObjectUtil.isNotEmpty(enemyMsgListAiParam.getInHistory())) {
            queryWrapper.lambda().eq(EnemyMsg::getInHistory, enemyMsgListAiParam.getInHistory());
        }

        queryWrapper.lambda().orderByDesc(EnemyMsg::getCreateTime).orderByDesc(EnemyMsg::getUpdateTime).orderByDesc(EnemyMsg::getContentTime);
        List<EnemyMsg> enemyMsgList = this.list(queryWrapper);

        // 更新AI检测状态
        if (CollectionUtils.isNotEmpty(enemyMsgList)) {
            String setAiDetectStatus = enemyMsgListAiParam.getSetAiDetectStatus();
            if (StringUtils.isNotBlank(setAiDetectStatus)) {
                // 更新AI检测状态
                EnemyMsg enemyMsgUpdate = new EnemyMsg();
                enemyMsgUpdate.setAiDetectStatus(setAiDetectStatus);
                this.update(enemyMsgUpdate, queryWrapper);
            }
        }

        return enemyMsgList;
    }

    @Override
    public void addAiDistinct(List<EnemyMsgAiResultParam> enemyMsgAiResultParamList) {
        if (CollectionUtils.isNotEmpty(enemyMsgAiResultParamList)) {
            // 待发送的模板和线索列表
            Map<String, List<JSONObject>> templateAndEnemyMsgListMap = new HashMap<>();
            for (EnemyMsgAiResultParam enemyMsgAiResultParam : enemyMsgAiResultParamList) {
                String msgId = enemyMsgAiResultParam.getMsgId();
                Long duplicateCount = enemyMsgAiResultParam.getDuplicateCount();
                EnemyMsg enemyMsg = this.getById(msgId);
                if (enemyMsg != null) {
                    if (duplicateCount != null) {
                        enemyMsg.setAiDistinctResult(String.valueOf(duplicateCount));
                    }
                    enemyMsg.setAiDistinctContent("保留");
                    this.updateById(enemyMsg);

                    // 是否发送告警
                    Boolean isSend = Boolean.FALSE;
                    String sendDDRobotMessageTemplate = null;
                    if (StringUtils.equalsIgnoreCase(enemyMsg.getSourceType(), "tieba")) {
//                        sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_TIEBA_MD_TEMPLATE;
                        // (贴吧)内容异常则发送告警
//                        if (CollectionUtils.containsAny(abResultList, "1")) {
//                            isSend = Boolean.TRUE;
//                        }
                    } else if (StringUtils.equalsIgnoreCase(enemyMsg.getSourceType(), "goofish")) {
                        sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_GOOFISH_DISTINCT_MD_TEMPLATE;
                        // 闲鱼全部发送告警
//                        isSend = Boolean.TRUE;
                    } else if (StringUtils.equalsIgnoreCase(enemyMsg.getSourceType(), "wechat")) {
//                        sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_WECHAT_MD_TEMPLATE;
                        // (微信群)内容异常则发送告警
//                        if (CollectionUtils.containsAny(abResultList, "1")) {
//                            isSend = Boolean.TRUE;
//                        }
                    } else if (StringUtils.equalsIgnoreCase(enemyMsg.getSourceType(), "xiansuo")) {
//                        sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_XIANSUO_MD_TEMPLATE;
//                        // 线索全部发送告警
//                        isSend = Boolean.TRUE;
                    }
                    if (isSend) {
                        JSONObject enemyMsgJSONObject = JSONObject.parseObject(JSONObject.toJSONString(enemyMsg));
                        enemyMsgJSONObject.put("data", JSONObject.parseObject(enemyMsg.getDataJson()));
                        List<JSONObject> enemyMsgList = templateAndEnemyMsgListMap.getOrDefault(sendDDRobotMessageTemplate, new ArrayList<>());
                        CollectionUtils.addIgnoreNull(enemyMsgList, enemyMsgJSONObject);
                        templateAndEnemyMsgListMap.put(sendDDRobotMessageTemplate, enemyMsgList);
                    }
                }
            }
            if (MapUtils.isNotEmpty(templateAndEnemyMsgListMap)) {
                for (String sendDDRobotMessageTemplate : templateAndEnemyMsgListMap.keySet()) {
                    List<JSONObject> enemyMsgList = templateAndEnemyMsgListMap.get(sendDDRobotMessageTemplate);
                    JSONObject templateData = new JSONObject();
                    templateData.put("enemyMsgList", enemyMsgList);
                    templateData.put("sendTime", DateUtil.now());
                    SendRobotDTO sendRobotDTO = messageService.generateSendDDRobotMessage(sendDDRobotMessageTemplate, templateData, "markdown", enemyMsgToken);
                    messageService.sendDDRobot(sendRobotDTO);
                }
            }
        }

    }

    @Override
    public void addAiDetect(List<EnemyMsgAiResultParam> enemyMsgAiResultParamList) {
        if (CollectionUtils.isNotEmpty(enemyMsgAiResultParamList)) {
            // 待发送的模板和线索列表
            Map<String, Map<String, List<JSONObject>>> templateAndEnemyMsgListMap = new HashMap<>();
            // 所有检测结果的线索列表，用于发送邮件
            Map<String, List<JSONObject>> allAiDetectResultGroups = new HashMap<>();
            // 新敌情结果分组（inHistory=0的记录）
            Map<String, List<JSONObject>> newEnemyResultGroups = new HashMap<>();
            // 已掌握敌情结果分组（inHistory=1的记录）
            Map<String, List<JSONObject>> knownEnemyResultGroups = new HashMap<>();

            for (EnemyMsgAiResultParam enemyMsgAiResultParam : enemyMsgAiResultParamList) {
                String msgId = enemyMsgAiResultParam.getMsgId();
                String aiDetectResult = StringUtils.defaultIfBlank(enemyMsgAiResultParam.getAiDetectResult(), "NULL");
                Long duplicateCount = enemyMsgAiResultParam.getDuplicateCount();
                String otherCategories = enemyMsgAiResultParam.getOtherCategories();
                Boolean inHistory = enemyMsgAiResultParam.getInHistory();
                Double similarityScore = enemyMsgAiResultParam.getSimilarityScore();

                EnemyMsg enemyMsg = this.getById(msgId);
                if (enemyMsg != null) {
//                    enemyMsg.setAiDistinctResult(JSONObject.toJSONString(Arrays.asList("1")));
//                    enemyMsg.setAiDistinctContent("保留");
                    if (duplicateCount != null) {
                        enemyMsg.setAiDistinctResult(String.valueOf(duplicateCount));
                    }
                    enemyMsg.setAiDetectResult(JSONObject.toJSONString(Arrays.asList(aiDetectResult)));
//                    enemyMsg.setAiDetectContent(aiDetectResult);

                    // 设置新增字段
                    if (StringUtils.isNotBlank(otherCategories)) {
                        enemyMsg.setOtherCategories(otherCategories);
                    }
                    if (inHistory != null) {
                        enemyMsg.setInHistory(inHistory ? 1 : 0);
                    }
                    if (similarityScore != null) {
                        enemyMsg.setSimilarityScore(BigDecimal.valueOf(similarityScore));
                    }

                    this.updateById(enemyMsg);

                    // 是否发送告警
                    Boolean isSend = Boolean.FALSE;
                    String sendDDRobotMessageTemplate = null;
                    if (StringUtils.equalsIgnoreCase(enemyMsg.getSourceType(), "tieba")) {
//                        sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_TIEBA_MD_TEMPLATE;
                        // (贴吧)内容异常则发送告警
//                        if (CollectionUtils.containsAny(abResultList, "1")) {
//                            isSend = Boolean.TRUE;
//                        }
                    } else if (StringUtils.equalsIgnoreCase(enemyMsg.getSourceType(), "goofish")) {
                        sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_GOOFISH_DISTINCT_MD_TEMPLATE;
                        // 闲鱼全部发送告警
                        isSend = Boolean.TRUE;
                    } else if (StringUtils.equalsIgnoreCase(enemyMsg.getSourceType(), "wechat")) {
//                        sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_WECHAT_MD_TEMPLATE;
                        // (微信群)内容异常则发送告警
//                        if (CollectionUtils.containsAny(abResultList, "1")) {
//                            isSend = Boolean.TRUE;
//                        }
                    } else if (StringUtils.equalsIgnoreCase(enemyMsg.getSourceType(), "xiansuo")) {
//                        sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_XIANSUO_MD_TEMPLATE;
//                        // 线索全部发送告警
//                        isSend = Boolean.TRUE;
                    }

                    // 处理链接数据用于发送邮件
                    JSONObject enemyMsgJSONObject = JSONObject.parseObject(JSONObject.toJSONString(enemyMsg));
                    enemyMsgJSONObject.put("data", JSONObject.parseObject(enemyMsg.getDataJson()));
                    String aiDetectResultZh = StringUtils.join(JSONObject.parseArray(enemyMsg.getAiDetectResult()), ",");
                    enemyMsgJSONObject.put("aiDetectResultZh", aiDetectResultZh);

                    // 添加到邮件分组中
                    List<JSONObject> groupList = allAiDetectResultGroups.getOrDefault(aiDetectResultZh, new ArrayList<>());
                    CollectionUtils.addIgnoreNull(groupList, enemyMsgJSONObject);
                    allAiDetectResultGroups.put(aiDetectResultZh, groupList);


                    // 如果是新敌情（inHistory=0），也添加到新敌情分组中
                    if (inHistory != null && !inHistory) {
                        List<JSONObject> newEnemyGroupList = newEnemyResultGroups.getOrDefault(aiDetectResultZh, new ArrayList<>());
                        CollectionUtils.addIgnoreNull(newEnemyGroupList, enemyMsgJSONObject);
                        newEnemyResultGroups.put(aiDetectResultZh, newEnemyGroupList);
                    } else {
                        // 已掌握敌情（inHistory=1）
                        List<JSONObject> knownEnemyGroupList = knownEnemyResultGroups.getOrDefault(aiDetectResultZh, new ArrayList<>());
                        CollectionUtils.addIgnoreNull(knownEnemyGroupList, enemyMsgJSONObject);
                        knownEnemyResultGroups.put(aiDetectResultZh, knownEnemyGroupList);
                    }

                    // 处理链接数据用于发送钉钉消息
                    if (isSend) {
                        // 该模板的检测结果和线索列表
                        Map<String, List<JSONObject>> aiDetectResultZhAndEnemyMsgListMap = templateAndEnemyMsgListMap.getOrDefault(sendDDRobotMessageTemplate, new HashMap<>());
                        // 该检测结果的线索列表
                        List<JSONObject> enemyMsgList = aiDetectResultZhAndEnemyMsgListMap.getOrDefault(aiDetectResultZh, new ArrayList<>());
                        CollectionUtils.addIgnoreNull(enemyMsgList, enemyMsgJSONObject);
                        aiDetectResultZhAndEnemyMsgListMap.put(aiDetectResultZh, enemyMsgList);
                        templateAndEnemyMsgListMap.put(sendDDRobotMessageTemplate, aiDetectResultZhAndEnemyMsgListMap);
                    }
                }
            }

            // 发送钉钉消息(暂不发送钉钉群消息)
//            if (MapUtils.isNotEmpty(templateAndEnemyMsgListMap)) {
//                for (String sendDDRobotMessageTemplate : templateAndEnemyMsgListMap.keySet()) {
//                    // 该模板的检测结果和线索列表
//                    Map<String, List<JSONObject>> aiDetectResultZhAndEnemyMsgListMap = templateAndEnemyMsgListMap.getOrDefault(sendDDRobotMessageTemplate, new HashMap<>());
//                    if (MapUtils.isNotEmpty(aiDetectResultZhAndEnemyMsgListMap)) {
//                        for (String aiDetectResultZh : aiDetectResultZhAndEnemyMsgListMap.keySet()) {
//                            List<JSONObject> enemyMsgList = aiDetectResultZhAndEnemyMsgListMap.get(aiDetectResultZh);
//                            JSONObject templateData = new JSONObject();
//                            templateData.put("enemyMsgList", enemyMsgList);
//                            templateData.put("sendTime", DateUtil.now());
//                            templateData.put("aiDetectResultZh", aiDetectResultZh);
//                            SendRobotDTO sendRobotDTO = messageService.generateSendDDRobotMessage(sendDDRobotMessageTemplate, templateData, "markdown", enemyMsgToken);
//                            messageService.sendDDRobot(sendRobotDTO);
//                        }
//                    }
//                }
//            }

            // 发送邮件（合并所有检测结果）
            if (MapUtils.isNotEmpty(allAiDetectResultGroups)) {
                // 解析邮件收件人和抄送人
                List<String> emailToList = new ArrayList<>();
                if (StringUtils.isNotBlank(enemyMsgEmailTo)) {
                    String[] emailToArray = StringUtils.split(enemyMsgEmailTo, ",");
                    if (emailToArray != null) {
                        emailToList.addAll(Arrays.asList(emailToArray));
                    }
                }

                List<String> emailCcList = new ArrayList<>();
                if (StringUtils.isNotBlank(enemyMsgEmailCc)) {
                    String[] emailCcArray = StringUtils.split(enemyMsgEmailCc, ",");
                    if (emailCcArray != null) {
                        emailCcList.addAll(Arrays.asList(emailCcArray));
                    }
                }

                // 如果有收件人，才发送邮件
                if (CollectionUtils.isNotEmpty(emailToList)) {
                    JSONObject templateData = new JSONObject();
                    // 所有敌情
//                    templateData.put("aiDetectResultGroups", allAiDetectResultGroups);
                    templateData.put("newEnemyResultGroups", newEnemyResultGroups);
                    templateData.put("knownEnemyResultGroups", knownEnemyResultGroups);
                    templateData.put("sendTime", DateUtil.now());

                    // 生成统计图表
                    List<ChartImageDTO> chartImages = new ArrayList<>();

                    // 生成最近7日新敌情趋势图
                    ChartImageDTO newEnemyTrendChart = generateNewEnemyTrendChart();
                    if (newEnemyTrendChart != null) {
                        chartImages.add(newEnemyTrendChart);
                    }

                    // 生成最近7日敌情线索来源趋势图
                    ChartImageDTO sourceTrendChart = generateSourceTrendChart();
                    if (sourceTrendChart != null) {
                        chartImages.add(sourceTrendChart);
                    }

                    // 生成最近7日关键词命中趋势图
                    ChartImageDTO keywordTrendChart = generateKeywordTrendChart();
                    if (keywordTrendChart != null) {
                        chartImages.add(keywordTrendChart);
                    }

                    // 生成最近7日异常分类趋势图
                    ChartImageDTO aiDetectResultTrendChart = generateAiDetectResultTrendChart();
                    if (aiDetectResultTrendChart != null) {
                        chartImages.add(aiDetectResultTrendChart);
                    }

                    templateData.put("chartImages", chartImages);

                    SendEmailDTO sendEmailDTO = messageService.generateSendEmailMessage(
                            MessageConstant.SEND_EMAIL_ENEMY_MESSAGE_AI_DETECT_TEMPLATE,
                            templateData,
                            "JJSIS 服务通知 - 安全威胁情报汇总日报",
                            emailToList,
                            emailCcList);

                    messageService.sendEmail(sendEmailDTO);
                }
            }
        }
    }

    /**
     * 生成最近7日关键词命中趋势图
     *
     * @return 图表图片数据传输对象
     */
    private ChartImageDTO generateKeywordTrendChart() {
        try {
            // 查询最近7天的关键词命中数据
            String endDate = DateUtil.today();
            String startDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.date(), -6));

            // 查询数据（每天前5个关键词）
            List<EnemyMsgKeywordTrendDTO> keywordStatList = this.baseMapper.statKeywordTrend(startDate, endDate);
            if (CollectionUtils.isEmpty(keywordStatList)) {
                return null;
            }

            // 数据处理
            List<String> days = new ArrayList<>();
            Map<String, List<Long>> keywordCountMap = new HashMap<>();

            // 首先收集所有日期和关键词
            Set<String> allKeywords = new HashSet<>();
            for (EnemyMsgKeywordTrendDTO stat : keywordStatList) {
                String day = stat.getDay();
                String keyword = stat.getHitKeyword();
                if (!days.contains(day)) {
                    days.add(day);
                }
                allKeywords.add(keyword);
            }

            // 按日期排序
            Collections.sort(days);

            // 初始化每个关键词的数据数组
            for (String keyword : allKeywords) {
                keywordCountMap.put(keyword, new ArrayList<>());
                // 填充每一天的数据，默认为0
                for (int i = 0; i < days.size(); i++) {
                    keywordCountMap.get(keyword).add(0L);
                }
            }

            // 填充实际数据
            for (EnemyMsgKeywordTrendDTO stat : keywordStatList) {
                String day = stat.getDay();
                String keyword = stat.getHitKeyword();
                Long count = stat.getCount();

                int dayIndex = days.indexOf(day);
                if (dayIndex >= 0) {
                    keywordCountMap.get(keyword).set(dayIndex, count);
                }
            }

            // 创建图表
            Line line = EchartsJavaUtils.defaultLineStyle("关键词命中次数", "命中次数", null, "日期", days.toArray());

            // 添加每个关键词的数据系列
            for (String keyword : allKeywords) {
                LineSeries lineSeries = EchartsJavaUtils.defaultLineSeriesStyle();
                lineSeries.setName(keyword);
                lineSeries.setData(keywordCountMap.get(keyword).toArray());
                line.addSeries(lineSeries);
            }

            // 生成图表
            Engine engine = new Engine();
            String optionString = engine.renderJsonOption(line);
            EChartsExportParamDTO eChartsExportParamDTO = new EChartsExportParamDTO();
            eChartsExportParamDTO.setBase64(Boolean.TRUE);
            eChartsExportParamDTO.setDownload(Boolean.FALSE);
            eChartsExportParamDTO.setWidth(820);
            eChartsExportParamDTO.setHeight(410);
            eChartsExportParamDTO.setOption(JSONObject.parseObject(optionString));

            JSONObject result = eChartsService.generateChart(eChartsExportParamDTO);
            if (Integer.valueOf(200).equals(result.getInteger("code"))) {
                String base64 = result.getString("data");
                ChartImageDTO chartImageDTO = new ChartImageDTO();
                chartImageDTO.setTitle("关键词命中趋势");
                chartImageDTO.setDescription("展示最近7天内各关键词的命中次数变化趋势(去重后)，只显示每天Top5的关键词。");
                chartImageDTO.setBase64(StringUtils.substringAfter(base64, ","));
                return chartImageDTO;
            }
        } catch (Exception e) {
            log.error("生成最近7日关键词命中趋势图失败", e);
        }
        return null;
    }

    /**
     * 生成最近7日异常分类趋势图
     *
     * @return 图表图片数据传输对象
     */
    private ChartImageDTO generateAiDetectResultTrendChart() {
        try {
            // 查询最近7天的异常分类数据
            String endDate = DateUtil.today();
            String startDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.date(), -6));

            // 查询数据（数据库已经返回了每天前5个异常分类）
            List<EnemyMsgAiDetectTrendDTO> aiDetectResultStatList = this.baseMapper.statAiDetectResultTrend(startDate, endDate);
            if (CollectionUtils.isEmpty(aiDetectResultStatList)) {
                return null;
            }

            // 数据处理
            List<String> days = new ArrayList<>();
            Map<String, List<Long>> aiDetectResultCountMap = new HashMap<>();

            // 首先收集所有日期和异常分类
            Set<String> allAiDetectResults = new HashSet<>();
            for (EnemyMsgAiDetectTrendDTO stat : aiDetectResultStatList) {
                String day = stat.getDay();
                String aiDetectResult = stat.getAiDetectResult();
                if (!days.contains(day)) {
                    days.add(day);
                }
                allAiDetectResults.add(aiDetectResult);
            }

            // 按日期排序
            Collections.sort(days);

            // 初始化每个异常分类的数据数组
            for (String aiDetectResult : allAiDetectResults) {
                aiDetectResultCountMap.put(aiDetectResult, new ArrayList<>());
                // 填充每一天的数据，默认为0
                for (int i = 0; i < days.size(); i++) {
                    aiDetectResultCountMap.get(aiDetectResult).add(0L);
                }
            }

            // 填充实际数据
            for (EnemyMsgAiDetectTrendDTO stat : aiDetectResultStatList) {
                String day = stat.getDay();
                String aiDetectResult = stat.getAiDetectResult();
                Long count = stat.getCount();

                int dayIndex = days.indexOf(day);
                if (dayIndex >= 0) {
                    aiDetectResultCountMap.get(aiDetectResult).set(dayIndex, count);
                }
            }

            // 数据库查询已经返回了前5个异常分类，直接使用所有异常分类

            // 创建图表
            Line line = EchartsJavaUtils.defaultLineStyle("异常分类命中次数", "命中次数", null, "日期", days.toArray());

            // 添加每个异常分类的数据系列
            for (String aiDetectResult : allAiDetectResults) {
                LineSeries lineSeries = EchartsJavaUtils.defaultLineSeriesStyle();
                lineSeries.setName(aiDetectResult);
                lineSeries.setData(aiDetectResultCountMap.get(aiDetectResult).toArray());
                line.addSeries(lineSeries);
            }

            // 生成图表
            Engine engine = new Engine();
            String optionString = engine.renderJsonOption(line);
            EChartsExportParamDTO eChartsExportParamDTO = new EChartsExportParamDTO();
            eChartsExportParamDTO.setBase64(Boolean.TRUE);
            eChartsExportParamDTO.setDownload(Boolean.FALSE);
            eChartsExportParamDTO.setWidth(820);
            eChartsExportParamDTO.setHeight(410);
            eChartsExportParamDTO.setOption(JSONObject.parseObject(optionString));

            JSONObject result = eChartsService.generateChart(eChartsExportParamDTO);
            if (Integer.valueOf(200).equals(result.getInteger("code"))) {
                String base64 = result.getString("data");
                ChartImageDTO chartImageDTO = new ChartImageDTO();
                chartImageDTO.setTitle("异常分类命中趋势");
                chartImageDTO.setDescription("展示最近7天内各异常分类的命中次数变化趋势(去重后)，只显示每天Top5的异常分类。");
                chartImageDTO.setBase64(StringUtils.substringAfter(base64, ","));
                return chartImageDTO;
            }
        } catch (Exception e) {
            log.error("生成最近7日异常分类趋势图失败", e);
        }
        return null;
    }

    /**
     * 生成最近7日敌情线索来源趋势图
     *
     * @return 图表图片数据传输对象
     */
    private ChartImageDTO generateSourceTrendChart() {
        try {
            // 查询最近7天的敌情线索来源数据
            String endDate = DateUtil.today();
            String startDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.date(), -6));

            // 查询数据
            List<EnemyMsgSourceTrendDTO> sourceStatList = this.baseMapper.statSourceTrend(startDate, endDate);
            if (CollectionUtils.isEmpty(sourceStatList)) {
                return null;
            }

            // 生成日期列表（最近7天）
            List<String> days = new ArrayList<>();
            for (int i = 6; i >= 0; i--) {
                String day = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.date(), -i));
                days.add(day);
            }

            // 获取所有来源类型
            Set<String> allSourceTypes = sourceStatList.stream()
                    .map(EnemyMsgSourceTrendDTO::getSourceType)
                    .collect(Collectors.toSet());

            // 为每个来源类型创建数据数组
            Map<String, List<Long>> sourceCountMap = new HashMap<>();
            for (String sourceType : allSourceTypes) {
                List<Long> counts = new ArrayList<>();
                for (int i = 0; i < days.size(); i++) {
                    counts.add(0L);
                }
                sourceCountMap.put(sourceType, counts);
            }

            // 填充实际数据
            for (EnemyMsgSourceTrendDTO stat : sourceStatList) {
                String day = stat.getDay();
                String sourceType = stat.getSourceType();
                Long count = stat.getCount();

                int dayIndex = days.indexOf(day);
                if (dayIndex >= 0) {
                    sourceCountMap.get(sourceType).set(dayIndex, count);
                }
            }

            // 创建图表
            Line line = EchartsJavaUtils.defaultLineStyle("敌情线索来源趋势", "线索数量", null, "日期", days.toArray());

            // 添加每个来源类型的数据系列
            for (String sourceType : allSourceTypes) {
                LineSeries lineSeries = EchartsJavaUtils.defaultLineSeriesStyle();
                // 转换来源类型显示名称
                String displayName = getSourceTypeDisplayName(sourceType);
                lineSeries.setName(displayName);
                lineSeries.setData(sourceCountMap.get(sourceType).toArray());
                line.addSeries(lineSeries);
            }

            // 生成图表
            Engine engine = new Engine();
            String optionString = engine.renderJsonOption(line);
            EChartsExportParamDTO eChartsExportParamDTO = new EChartsExportParamDTO();
            eChartsExportParamDTO.setBase64(Boolean.TRUE);
            eChartsExportParamDTO.setDownload(Boolean.FALSE);
            eChartsExportParamDTO.setWidth(820);
            eChartsExportParamDTO.setHeight(410);
            eChartsExportParamDTO.setOption(JSONObject.parseObject(optionString));

            JSONObject result = eChartsService.generateChart(eChartsExportParamDTO);
            if (Integer.valueOf(200).equals(result.getInteger("code"))) {
                String base64 = result.getString("data");
                ChartImageDTO chartImageDTO = new ChartImageDTO();
                chartImageDTO.setTitle("敌情线索来源趋势");
                chartImageDTO.setDescription("展示最近7天内各来源平台的敌情线索数量变化趋势，包括贴吧、闲鱼、微信群、QQ群等平台。");
                chartImageDTO.setBase64(StringUtils.substringAfter(base64, ","));
                return chartImageDTO;
            }
        } catch (Exception e) {
            log.error("生成最近7日敌情线索来源趋势图失败", e);
        }
        return null;
    }

    /**
     * 获取来源类型显示名称
     *
     * @param sourceType 来源类型
     * @return 显示名称
     */
    private String getSourceTypeDisplayName(String sourceType) {
        if (StringUtils.isBlank(sourceType)) {
            return "未知";
        }
        switch (sourceType) {
            case "tieba":
                return "贴吧";
            case "goofish":
                return "闲鱼";
            case "taobao":
                return "淘宝";
            case "wechat":
                return "微信群";
            case "qq":
                return "QQ群";
            default:
                return sourceType;
        }
    }


    /**
     * 生成最近7日新敌情趋势图
     *
     * @return 图表图片数据传输对象
     */
    private ChartImageDTO generateNewEnemyTrendChart() {
        try {
            // 查询最近7天的新敌情数据
            String endDate = DateUtil.today();
            String startDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.date(), -6));

            // 查询数据
            List<EnemyMsgNewEnemyTrendDTO> newEnemyStatList = this.baseMapper.statNewEnemyTrend(startDate, endDate);
            if (CollectionUtils.isEmpty(newEnemyStatList)) {
                return null;
            }

            // 生成日期列表（最近7天）
            List<String> days = new ArrayList<>();
            for (int i = 6; i >= 0; i--) {
                String day = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.date(), -i));
                days.add(day);
            }

            // 获取所有异常分类
            Set<String> allAiDetectResults = newEnemyStatList.stream()
                    .map(EnemyMsgNewEnemyTrendDTO::getAiDetectResult)
                    .collect(Collectors.toSet());

            // 为每个异常分类创建数据数组
            Map<String, List<Long>> aiDetectResultCountMap = new HashMap<>();
            for (String aiDetectResult : allAiDetectResults) {
                List<Long> counts = new ArrayList<>();
                for (int i = 0; i < days.size(); i++) {
                    counts.add(0L);
                }
                aiDetectResultCountMap.put(aiDetectResult, counts);
            }

            // 填充实际数据
            for (EnemyMsgNewEnemyTrendDTO stat : newEnemyStatList) {
                String day = stat.getDay();
                String aiDetectResult = stat.getAiDetectResult();
                Long count = stat.getCount();

                int dayIndex = days.indexOf(day);
                if (dayIndex >= 0) {
                    aiDetectResultCountMap.get(aiDetectResult).set(dayIndex, count);
                }
            }

            // 创建图表
            Line line = EchartsJavaUtils.defaultLineStyle("新敌情趋势", "新敌情数量", null, "日期", days.toArray());

            // 添加每个异常分类的数据系列
            for (String aiDetectResult : allAiDetectResults) {
                LineSeries lineSeries = EchartsJavaUtils.defaultLineSeriesStyle();
                lineSeries.setName(aiDetectResult);
                lineSeries.setData(aiDetectResultCountMap.get(aiDetectResult).toArray());
                line.addSeries(lineSeries);
            }

            // 生成图表
            Engine engine = new Engine();
            String optionString = engine.renderJsonOption(line);
            EChartsExportParamDTO eChartsExportParamDTO = new EChartsExportParamDTO();
            eChartsExportParamDTO.setBase64(Boolean.TRUE);
            eChartsExportParamDTO.setDownload(Boolean.FALSE);
            eChartsExportParamDTO.setWidth(820);
            eChartsExportParamDTO.setHeight(410);
            eChartsExportParamDTO.setOption(JSONObject.parseObject(optionString));

            JSONObject result = eChartsService.generateChart(eChartsExportParamDTO);
            if (Integer.valueOf(200).equals(result.getInteger("code"))) {
                String base64 = result.getString("data");
                ChartImageDTO chartImageDTO = new ChartImageDTO();
                chartImageDTO.setTitle("新敌情趋势");
                chartImageDTO.setDescription("展示最近7天内各异常分类的新敌情数量变化趋势(去重后)，只显示每天Top5的异常分类。");
                chartImageDTO.setBase64(StringUtils.substringAfter(base64, ","));
                return chartImageDTO;
            }
        } catch (Exception e) {
            log.error("生成最近7日新敌情趋势图失败", e);
        }
        return null;
    }

    @Override
    public EnemyMsg queryEntity(String id) {
        EnemyMsg enemyMsg = this.getById(id);
        if (ObjectUtil.isEmpty(enemyMsg)) {
            throw new CommonException("敌情线索-线索表不存在，id值为：{}", id);
        }
        return enemyMsg;
    }

    @Override
    public void add(JSONObject msgJSONObject) {
        if (msgJSONObject != null) {
            String channel = msgJSONObject.getString("channel");
            String msgStr = JSONObject.toJSONString(msgJSONObject);
            if (StringUtils.equalsIgnoreCase(channel, "goofish")) {
                // 闲鱼
                GoofishMsgDTO goofishMsgDTO = JSONObject.parseObject(msgStr, GoofishMsgDTO.class);
                EnemyMsgAddParam enemyMsgAddParam = new EnemyMsgAddParam();
                enemyMsgAddParam.setSourceType(channel);
                enemyMsgAddParam.setHitKeyword(goofishMsgDTO.getKeyword());
                enemyMsgAddParam.setContentName(goofishMsgDTO.getTitle());
                enemyMsgAddParam.setContentId(goofishMsgDTO.getItemid());
                enemyMsgAddParam.setContentUser(goofishMsgDTO.getUserNick());
                enemyMsgAddParam.setContentLink(goofishMsgDTO.getUrl());
                enemyMsgAddParam.setContentTime(goofishMsgDTO.getPublishTime());
                enemyMsgAddParam.setDataJson(msgStr);
                this.add(enemyMsgAddParam);
            } else if (StringUtils.equalsIgnoreCase(channel, "tieba")) {
                // 贴吧
                TiebaMsgDTO tiebaMsgDTO = JSONObject.parseObject(msgStr, TiebaMsgDTO.class);
                EnemyMsgAddParam enemyMsgAddParam = new EnemyMsgAddParam();
                enemyMsgAddParam.setSourceType(channel);
                enemyMsgAddParam.setHitKeyword(tiebaMsgDTO.getKeyword());
                enemyMsgAddParam.setContentName(tiebaMsgDTO.getText());
                // floor 字段未使用
//                tiebaMsgDTO.getFloor();
                enemyMsgAddParam.setContentId(String.valueOf(tiebaMsgDTO.getTid()));
                enemyMsgAddParam.setContentUser(tiebaMsgDTO.getUserNick());
                enemyMsgAddParam.setContentLink(tiebaMsgDTO.getUrl());
                enemyMsgAddParam.setContentTime(tiebaMsgDTO.getPublishTime());
                enemyMsgAddParam.setDataJson(msgStr);
                this.add(enemyMsgAddParam);
            } else if (StringUtils.equalsIgnoreCase(channel, "wechat")) {
                // 微信群
                WechatMsgDTO wechatMsgDTO = JSONObject.parseObject(msgStr, WechatMsgDTO.class);
                EnemyMsgAddParam enemyMsgAddParam = new EnemyMsgAddParam();
                enemyMsgAddParam.setSourceType(channel);
                enemyMsgAddParam.setHitKeyword(String.format("%s(%s)", wechatMsgDTO.getGroupName(), wechatMsgDTO.getUsersCnt()));

                WechatMsgDataDTO wechatMsgDataDTO = wechatMsgDTO.getData();
//                enemyMsgAddParam.setContentName(wechatMsgDataDTO.getMsg());
                // 微信群的转行符特征处理
                enemyMsgAddParam.setContentName(StringUtils.replace(wechatMsgDataDTO.getMsg(), "\\n", "\n"));
                enemyMsgAddParam.setContentId(wechatMsgDataDTO.getUserId());
                enemyMsgAddParam.setContentUser(StringUtils.isNotBlank(wechatMsgDataDTO.getDisplayname()) ? String.format("%s(%s)", wechatMsgDataDTO.getNickname(), wechatMsgDataDTO.getDisplayname()) : wechatMsgDataDTO.getNickname());
                enemyMsgAddParam.setContentTime(wechatMsgDataDTO.getMsgTime());
                enemyMsgAddParam.setDataJson(msgStr);
                this.add(enemyMsgAddParam);
            } else if (StringUtils.equalsIgnoreCase(channel, "qq")) {
                // QQ群
                QQMsgDTO qqMsgDTO = JSONObject.parseObject(msgStr, QQMsgDTO.class);
                EnemyMsgAddParam enemyMsgAddParam = new EnemyMsgAddParam();
                enemyMsgAddParam.setSourceType(channel);
                enemyMsgAddParam.setHitKeyword(qqMsgDTO.getGroupName());

                List<QQMsgDataDTO> data = qqMsgDTO.getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    for (QQMsgDataDTO qqMsgDataDTO : data) {
                        // QQ群的转行符特征处理
                        enemyMsgAddParam.setContentName(StringUtils.replace(qqMsgDataDTO.getMsg(), "\\n", "\n"));
                        enemyMsgAddParam.setContentId(qqMsgDataDTO.getUserId());
                        enemyMsgAddParam.setContentUser(StringUtils.isNotBlank(qqMsgDataDTO.getNickname()) ? qqMsgDataDTO.getNickname() : qqMsgDataDTO.getUserId());
                        enemyMsgAddParam.setContentTime(qqMsgDataDTO.getMsgTime());
                        // data特殊处理
                        JSONObject dataJSONObject = JSONObject.parseObject(msgStr);
                        dataJSONObject.put("data", qqMsgDataDTO);
                        enemyMsgAddParam.setDataJson(JSONObject.toJSONString(dataJSONObject));
                        this.add(enemyMsgAddParam);
                    }
                }
            } else if (StringUtils.equalsIgnoreCase(channel, "xiansuo")) {
                // 线索
                XiansuoMsgDTO xiansuoMsgDTO = JSONObject.parseObject(msgStr, XiansuoMsgDTO.class);
                EnemyMsgAddParam enemyMsgAddParam = new EnemyMsgAddParam();
                enemyMsgAddParam.setSourceType(channel);
                enemyMsgAddParam.setHitKeyword(xiansuoMsgDTO.getKeyword());

                // 从item_info中提取name作为内容名称，如果没有name则使用domain
                String contentName = null;
                if (xiansuoMsgDTO.getItemInfo() != null) {
                    contentName = StringUtils.firstNonBlank(xiansuoMsgDTO.getItemInfo().getString("name"), xiansuoMsgDTO.getItemInfo().getString("title"), xiansuoMsgDTO.getItemInfo().getString("fileName"));
                }
                if (StringUtils.isBlank(contentName)) {
                    contentName = xiansuoMsgDTO.getDomain();
                }
                enemyMsgAddParam.setContentName(contentName);

                // 从item_info中提取tid或id作为内容ID，如果没有则使用domain
                String contentId = null;
                if (xiansuoMsgDTO.getItemInfo() != null) {
                    contentId = StringUtils.firstNonBlank(xiansuoMsgDTO.getItemInfo().getString("id"), xiansuoMsgDTO.getItemInfo().getString("fileId"), xiansuoMsgDTO.getItemInfo().getString("tid"), xiansuoMsgDTO.getItemInfo().getString("cid"));
                }
                if (StringUtils.isBlank(contentId)) {
                    contentId = xiansuoMsgDTO.getDomain();
                }
                enemyMsgAddParam.setContentId(contentId);

                enemyMsgAddParam.setContentUser(xiansuoMsgDTO.getDomain());
                enemyMsgAddParam.setContentLink(xiansuoMsgDTO.getUrl());
                // 内容时间
                enemyMsgAddParam.setContentTime(xiansuoMsgDTO.getCreateTime());
                enemyMsgAddParam.setDataJson(msgStr);

                // 线索消息立即发送告警，不需要等待AI识别
                this.add(enemyMsgAddParam);

                // 立即发送钉钉告警
                this.sendXiansuoAlarm(enemyMsgAddParam);
            } else {
                log.warn("接收到未知渠道消息，msgJSONObject={}", JSONObject.toJSONString(msgJSONObject));
            }
        }
    }

    @Override
    public List<EnemyMsgAiDetectStatDTO> statAiDetectResult(EnemyMsgAiDetectStatParam enemyMsgAiDetectStatParam) {
        // 如果没有指定开始日期，默认为最近7天
        String startDate = enemyMsgAiDetectStatParam.getStartDate();
        if (StringUtils.isBlank(startDate)) {
            startDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.date(), -6));
        }

        // 如果没有指定结束日期，默认为当前日期
        String endDate = enemyMsgAiDetectStatParam.getEndDate();
        if (StringUtils.isBlank(endDate)) {
            endDate = DateUtil.today();
        }

        // 调用Mapper方法执行统计查询
        return this.baseMapper.statAiDetectResult(startDate, endDate, enemyMsgAiDetectStatParam.getSourceType());
    }

    /**
     * 发送线索告警
     *
     * @param enemyMsgAddParam 敌情线索添加参数
     */
    private void sendXiansuoAlarm(EnemyMsgAddParam enemyMsgAddParam) {
        try {
            // 构建模板数据
            JSONObject templateData = JSONObject.parseObject(JSONObject.toJSONString(enemyMsgAddParam));
            templateData.put("data", JSONObject.parseObject(enemyMsgAddParam.getDataJson()));
            templateData.put("sendTime", DateUtil.now());

            // 生成钉钉机器人消息
            SendRobotDTO sendRobotDTO = messageService.generateSendDDRobotMessage(MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_XIANSUO_MD_TEMPLATE, templateData, "markdown", enemyMsgToken);

            // 发送钉钉机器人消息
            messageService.sendDDRobot(sendRobotDTO);

            // 曙光英雄另外单独发送
            if (StringUtils.containsIgnoreCase(enemyMsgAddParam.getHitKeyword(), "曙光") || StringUtils.containsIgnoreCase(enemyMsgAddParam.getContentName(), "曙光")) {
                SendRobotDTO sendRobotDTOshuguang = messageService.generateSendDDRobotMessage(MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_XIANSUO_MD_TEMPLATE, templateData, "markdown", enemyMsgTokenShuguang);
                messageService.sendDDRobot(sendRobotDTOshuguang);
            }

            log.info("线索告警发送成功，关键词：[{}]，域名：[{}]，商品名：[{}]", enemyMsgAddParam.getHitKeyword(), enemyMsgAddParam.getContentUser(), enemyMsgAddParam.getContentName());
        } catch (Exception e) {
            log.error("发送线索告警失败", e);
        }
    }
}
