/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.enemy.modular.kw.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 关键字管理添加参数
 *
 * <AUTHOR>
 * @date 2025/01/02 16:48
 **/
@Getter
@Setter
public class EnemyKwAddParam {

    /**
     * 关键字类型，=tieba_hit 贴吧触发，=tieba_name 贴吧名称，=goofish_search 闲鱼搜索
     */
    @ApiModelProperty(value = "关键字类型，=tieba_hit 贴吧触发，=tieba_name 贴吧名称，=goofish_search 闲鱼搜索", position = 2)
    @NotBlank(message = "关键字类型不能为空")
    private String kwType;

    /**
     * 来源类型，=tieba 贴吧，=goofish 闲鱼，=taobao 淘宝，=wechat 微信群
     */
    @ApiModelProperty(value = "来源类型，=tieba 贴吧，=goofish 闲鱼，=taobao 淘宝，=wechat 微信群", position = 3)
    private String sourceType;

    /**
     * 关键词
     */
    @ApiModelProperty(value = "关键词", position = 4)
    @NotBlank(message = "关键词不能为空")
    private String kw;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", position = 5)
    private String remark;

    /**
     * 状态，-1删除，0无效，1有效
     */
    @ApiModelProperty(value = "状态，-1删除，0无效，1有效", position = 10)
    private String status;

}
