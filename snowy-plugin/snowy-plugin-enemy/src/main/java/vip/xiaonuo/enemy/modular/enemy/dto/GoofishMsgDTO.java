package vip.xiaonuo.enemy.modular.enemy.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 闲鱼消息传输对象
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/21 11:59
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "闲鱼消息传输对象")
public class GoofishMsgDTO implements Serializable {

    private static final Long serialVersionUID = 1L;
    /*
{
"itemid": "************" ,
"publishTime": "2024-12-21 11:48:28" ,
"userNick": "贝小贝承蒙厚爱" ,
"channel": "goofish" ,
"title": "jj斗地主 最新全自动打金脚本 一天300➕ [火][火][火][火][火][火] 利用脚本自动打牌，单机器300+ 通过脚本在自由场2小时自动打牌打到15000，豆儿直接出号，一个号6元。 单窗口一天轻松出10个号，利润很大 电脑模拟器多开 中控脚本" ,
"url": "https://www.goofish.com/item?spm=a21ybx.search.searchFeedList.1.2c563da6BMM5WE&id=************&categoryId=201409706"
}
     */

    @ApiModelProperty(value = "渠道，goofish 闲鱼", name = "channel", required = true)
    private String channel;

    @ApiModelProperty(value = "标题", name = "title", required = true)
    private String title;

    @ApiModelProperty(value = "触发关键词", name = "keyword", required = true)
    private String keyword;

    @ApiModelProperty(value = "用户昵称", name = "userNick", required = true)
    private String userNick;

    @ApiModelProperty(value = "商品ID", name = "itemid", required = true)
    private String itemid;

    @ApiModelProperty(value = "发布时间", name = "publishTime", required = true)
    private String publishTime;

    @ApiModelProperty(value = "链接", name = "url", required = true)
    private String url;
}
