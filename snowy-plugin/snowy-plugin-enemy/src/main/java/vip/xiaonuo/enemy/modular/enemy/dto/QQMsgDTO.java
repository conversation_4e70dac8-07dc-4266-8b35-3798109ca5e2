package vip.xiaonuo.enemy.modular.enemy.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/4/7 15:07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "QQ群消息传输对象")
public class QQMsgDTO implements Serializable {

    private static final Long serialVersionUID = 1L;
    /*
{
  "data": [
    {
      "msg": "上下分软件（雷神-周一2款）\\\\n解金币限制软件\\\\n限制号上下分软件（必须要白名单）",
      "group_id": "203534752",
      "user_id": "3875490336",
      "nick_name": "",
      "msg_time": "2025-03-19 15:22:57"
    },
    {
      "msg": "出一些5万经验号",
      "group_id": "203534752",
      "user_id": "275355473",
      "nick_name": "",
      "msg_time": "2025-03-23 20:42:23"
    },
    {
      "msg": "有上下分软件的加我",
      "group_id": "203534752",
      "user_id": "3875490336",
      "nick_name": "",
      "msg_time": "2025-03-26 16:57:26"
    }
  ],
  "group_name": "203534752",
  "channel": "qq"
}
     */

    @ApiModelProperty(value = "渠道，qq QQ群", name = "channel", required = true)
    private String channel;

    @ApiModelProperty(value = "群名称", name = "groupName", required = true)
    @JSONField(name = "group_name")
    private String groupName;

    @ApiModelProperty(value = "数据", name = "data", required = true)
    private List<QQMsgDataDTO> data;
}
