/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.enemy.modular.kw.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.enemy.core.constant.DataSourceConstant;
import vip.xiaonuo.enemy.modular.kw.entity.EnemyKw;
import vip.xiaonuo.enemy.modular.kw.mapper.EnemyKwMapper;
import vip.xiaonuo.enemy.modular.kw.param.EnemyKwAddParam;
import vip.xiaonuo.enemy.modular.kw.param.EnemyKwEditParam;
import vip.xiaonuo.enemy.modular.kw.param.EnemyKwIdParam;
import vip.xiaonuo.enemy.modular.kw.param.EnemyKwPageParam;
import vip.xiaonuo.enemy.modular.kw.service.EnemyKwService;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

/**
 * 关键字管理Service接口实现类
 *
 * <AUTHOR>
 * @date 2025/01/02 16:48
 **/
@Service
@DS(DataSourceConstant.DYNAMIC_DATASOURCE_ENEMY_MESSAGE)
@Slf4j
public class EnemyKwServiceImpl extends ServiceImpl<EnemyKwMapper, EnemyKw> implements EnemyKwService {

    @Override
    public Page<EnemyKw> page(EnemyKwPageParam enemyKwPageParam) {
        QueryWrapper<EnemyKw> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(enemyKwPageParam.getKwType())) {
            queryWrapper.lambda().eq(EnemyKw::getKwType, enemyKwPageParam.getKwType());
        }
        if (ObjectUtil.isNotEmpty(enemyKwPageParam.getSourceType())) {
            queryWrapper.lambda().eq(EnemyKw::getSourceType, enemyKwPageParam.getSourceType());
        }
        if (ObjectUtil.isNotEmpty(enemyKwPageParam.getKw())) {
            queryWrapper.lambda().like(EnemyKw::getKw, enemyKwPageParam.getKw());
        }
        if (ObjectUtil.isNotEmpty(enemyKwPageParam.getRemark())) {
            queryWrapper.lambda().like(EnemyKw::getRemark, enemyKwPageParam.getRemark());
        }
        if (ObjectUtil.isNotEmpty(enemyKwPageParam.getStatus())) {
            queryWrapper.lambda().eq(EnemyKw::getStatus, enemyKwPageParam.getStatus());
        }
        if (ObjectUtil.isAllNotEmpty(enemyKwPageParam.getSortField(), enemyKwPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(enemyKwPageParam.getSortOrder());
            queryWrapper.orderBy(true, enemyKwPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(enemyKwPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(EnemyKw::getCreateTime).orderByDesc(EnemyKw::getUpdateTime);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Override
    public List<EnemyKw> list(EnemyKwPageParam enemyKwPageParam) {
        QueryWrapper<EnemyKw> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(enemyKwPageParam.getKwType())) {
            queryWrapper.lambda().eq(EnemyKw::getKwType, enemyKwPageParam.getKwType());
        }
        if (ObjectUtil.isNotEmpty(enemyKwPageParam.getSourceType())) {
            queryWrapper.lambda().eq(EnemyKw::getSourceType, enemyKwPageParam.getSourceType());
        }
        if (ObjectUtil.isNotEmpty(enemyKwPageParam.getKw())) {
            queryWrapper.lambda().like(EnemyKw::getKw, enemyKwPageParam.getKw());
        }
        if (ObjectUtil.isNotEmpty(enemyKwPageParam.getRemark())) {
            queryWrapper.lambda().like(EnemyKw::getRemark, enemyKwPageParam.getRemark());
        }
        if (ObjectUtil.isNotEmpty(enemyKwPageParam.getStatus())) {
            queryWrapper.lambda().eq(EnemyKw::getStatus, enemyKwPageParam.getStatus());
        }
        if (ObjectUtil.isAllNotEmpty(enemyKwPageParam.getSortField(), enemyKwPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(enemyKwPageParam.getSortOrder());
            queryWrapper.orderBy(true, enemyKwPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(enemyKwPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(EnemyKw::getCreateTime).orderByDesc(EnemyKw::getUpdateTime);
        }
        return this.list(queryWrapper);
    }

    @Override
    public Set<String> listKwSet(EnemyKwPageParam enemyKwPageParam) {
        QueryWrapper<EnemyKw> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(enemyKwPageParam.getKwType())) {
            queryWrapper.lambda().eq(EnemyKw::getKwType, enemyKwPageParam.getKwType());
        }
        if (ObjectUtil.isNotEmpty(enemyKwPageParam.getSourceType())) {
            queryWrapper.lambda().eq(EnemyKw::getSourceType, enemyKwPageParam.getSourceType());
        }
        if (ObjectUtil.isNotEmpty(enemyKwPageParam.getKw())) {
            queryWrapper.lambda().like(EnemyKw::getKw, enemyKwPageParam.getKw());
        }
        if (ObjectUtil.isNotEmpty(enemyKwPageParam.getRemark())) {
            queryWrapper.lambda().like(EnemyKw::getRemark, enemyKwPageParam.getRemark());
        }
        if (ObjectUtil.isNotEmpty(enemyKwPageParam.getStatus())) {
            queryWrapper.lambda().eq(EnemyKw::getStatus, enemyKwPageParam.getStatus());
        }
        if (ObjectUtil.isAllNotEmpty(enemyKwPageParam.getSortField(), enemyKwPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(enemyKwPageParam.getSortOrder());
            queryWrapper.orderBy(true, enemyKwPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(enemyKwPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(EnemyKw::getCreateTime).orderByDesc(EnemyKw::getUpdateTime);
        }
        List<EnemyKw> enemyKwList = this.list(queryWrapper);
        Set<String> kwSet = new LinkedHashSet<>();
        if (CollectionUtils.isNotEmpty(enemyKwList)) {
            for (EnemyKw enemyKw : enemyKwList) {
                if (StringUtils.isNotBlank(enemyKw.getKw())) {
                    CollectionUtils.addIgnoreNull(kwSet, enemyKw.getKw());
                }
            }
        }
        return kwSet;
    }

    @Override
    public void add(EnemyKwAddParam enemyKwAddParam) {
        EnemyKw enemyKw = BeanUtil.toBean(enemyKwAddParam, EnemyKw.class);
        if (StringUtils.isNotBlank(enemyKw.getKwType())) {
            String sourceType = StringUtils.substringBefore(enemyKw.getKwType(), "_");
            if (StringUtils.isNotBlank(sourceType)) {
                enemyKw.setSourceType(sourceType);
            }
        }
        if (StringUtils.isBlank(enemyKw.getStatus())) {
            // 状态，-1删除，0无效，1有效
            enemyKw.setStatus("1");
        }
        this.save(enemyKw);
    }

    @Override
    public void edit(EnemyKwEditParam enemyKwEditParam) {
        EnemyKw enemyKw = this.queryEntity(enemyKwEditParam.getId());
        BeanUtil.copyProperties(enemyKwEditParam, enemyKw);
        if (StringUtils.isNotBlank(enemyKw.getKwType())) {
            String sourceType = StringUtils.substringBefore(enemyKw.getKwType(), "_");
            if (StringUtils.isNotBlank(sourceType)) {
                enemyKw.setSourceType(sourceType);
            }
        }
        if (StringUtils.isBlank(enemyKw.getStatus())) {
            // 状态，-1删除，0无效，1有效
            enemyKw.setStatus("1");
        }
        this.updateById(enemyKw);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<EnemyKwIdParam> enemyKwIdParamList) {
        // 执行删除
        this.removeBatchByIds(CollStreamUtil.toList(enemyKwIdParamList, EnemyKwIdParam::getId));
    }

    @Override
    public EnemyKw detail(EnemyKwIdParam enemyKwIdParam) {
        return this.queryEntity(enemyKwIdParam.getId());
    }

    @Override
    public EnemyKw queryEntity(String id) {
        EnemyKw enemyKw = this.getById(id);
        if (ObjectUtil.isEmpty(enemyKw)) {
            throw new CommonException("关键字管理不存在，id值为：{}", id);
        }
        return enemyKw;
    }
}
