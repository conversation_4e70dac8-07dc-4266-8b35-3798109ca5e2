package vip.xiaonuo.enemy;

import com.alibaba.fastjson.JSONObject;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 线索功能测试
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/31 15:30
 */
@SpringBootTest
public class XiansuoTest {

    @Test
    public void testXiansuoMessage() {
        // 构造测试数据 - 新的数据结构
        String testMessage = """
                {
                    "channel": "xiansuo",
                    "domain": "lilingyu1",
                    "name": "🔥iOS曙光英雄【黑豹】天卡/全图内透/免签名",
                    "create_time": "2025-07-31 14:45:08",
                    "url": "https://li.lilingyu1.cn",
                    "item_info": {
                        "tid": "46782",
                        "cid": "677",
                        "keyword": "曙光",
                        "desc": "iOS官方网盘：https://ios888.icu<br />\\r\\niOS官方网盘2：https://zs777.icu<br />\\r\\n<br />",
                        "price": 15.38,
                        "stock": "28",
                        "sales": "0",
                        "fileId": 12967460,
                        "size": 0
                    }
                }
                """;

        JSONObject msgJSONObject = JSONObject.parseObject(testMessage);
        System.out.println("测试消息：" + msgJSONObject.toJSONString());
        
        // 验证顶级字段
        String channel = msgJSONObject.getString("channel");
        String domain = msgJSONObject.getString("domain");
        String name = msgJSONObject.getString("name");
        String createTime = msgJSONObject.getString("create_time");
        String url = msgJSONObject.getString("url");
        
        System.out.println("渠道：" + channel);
        System.out.println("域名：" + domain);
        System.out.println("商品名：" + name);
        System.out.println("创建时间：" + createTime);
        System.out.println("链接：" + url);
        
        // 验证item_info中的字段
        JSONObject itemInfo = msgJSONObject.getJSONObject("item_info");
        if (itemInfo != null) {
            System.out.println("商品信息字段：");
            for (String key : itemInfo.keySet()) {
                System.out.println("  " + key + ": " + itemInfo.get(key));
            }
            
            // 特别验证keyword字段
            String keyword = itemInfo.getString("keyword");
            System.out.println("关键词：" + keyword);
        }
        
        // 这里可以调用EnemyMsgService的add方法进行测试
        // enemyMsgService.add(msgJSONObject);
    }

    @Test
    public void testXiansuoMessageWithEmptyFields() {
        // 测试空字段的情况 - 关键词和上架时间为空
        String testMessage = """
                {
                    "channel": "xiansuo",
                    "domain": "testdomain",
                    "name": "测试商品",
                    "url": "https://test.example.com",
                    "item_info": {
                        "tid": "12345",
                        "cid": "678",
                        "keyword": "",
                        "price": 25.99,
                        "stock": "10",
                        "sales": "5"
                    }
                }
                """;

        JSONObject msgJSONObject = JSONObject.parseObject(testMessage);
        System.out.println("测试消息（空字段）：" + msgJSONObject.toJSONString());
        
        // 验证空字段处理
        String createTime = msgJSONObject.getString("create_time");
        JSONObject itemInfo = msgJSONObject.getJSONObject("item_info");
        String keyword = itemInfo != null ? itemInfo.getString("keyword") : null;
        
        System.out.println("创建时间是否为空：" + (createTime == null || createTime.trim().isEmpty()));
        System.out.println("关键词是否为空：" + (keyword == null || keyword.trim().isEmpty()));
    }

    @Test
    public void testXiansuoMessageWithNullFields() {
        // 测试null字段的情况
        String testMessage = """
                {
                    "channel": "xiansuo",
                    "domain": "testdomain",
                    "name": "测试商品",
                    "url": "https://test.example.com",
                    "item_info": {
                        "tid": "12345",
                        "price": 25.99
                    }
                }
                """;

        JSONObject msgJSONObject = JSONObject.parseObject(testMessage);
        System.out.println("测试消息（null字段）：" + msgJSONObject.toJSONString());
        
        // 验证null字段处理
        boolean hasCreateTime = msgJSONObject.containsKey("create_time");
        JSONObject itemInfo = msgJSONObject.getJSONObject("item_info");
        boolean hasKeyword = itemInfo != null && itemInfo.containsKey("keyword");
        
        System.out.println("是否包含create_time字段：" + hasCreateTime);
        System.out.println("是否包含keyword字段：" + hasKeyword);
    }
}
