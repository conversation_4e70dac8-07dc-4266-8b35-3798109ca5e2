# 线索(xiansuo)功能说明

## 概述
线索功能用于处理来自各种渠道的线索信息，支持动态的商品信息字段和即时告警功能。

## 数据结构

### 输入数据格式
```json
{
    "channel": "xiansuo",
    "domain": "lilingyu1",
    "name": "🔥iOS曙光英雄【黑豹】天卡/全图内透/免签名",
    "create_time": "2025-07-31 14:45:08",
    "url": "https://li.lilingyu1.cn",
    "item_info": {
        "tid": "46782",
        "cid": "677",
        "keyword": "曙光",
        "desc": "iOS官方网盘：https://ios888.icu<br />\r\niOS官方网盘2：https://zs777.icu<br />\r\n<br />",
        "price": 15.38,
        "stock": "28",
        "sales": "0",
        "fileId": 12967460,
        "size": 0
    }
}
```

### 字段说明

#### 顶级字段
- **channel**: 固定为 "xiansuo"
- **domain**: 域名信息
- **name**: 商品名称
- **create_time**: 创建时间（格式：YYYY-MM-DD HH:mm:ss）
- **url**: 链接地址

#### item_info字段
- **tid**: 商品ID
- **cid**: 商家ID
- **keyword**: 关键字（触发关键词）
- **desc**: 商品介绍
- **price**: 商品价格
- **stock**: 商品库存
- **sales**: 商品销售量
- **fileId**: 文件ID
- **size**: 文件大小

### item_info字段映射
系统会将item_info中的英文字段名映射为中文显示名称：

| 英文字段名 | 中文显示名称 | 说明 |
|-----------|-------------|------|
| tid | 商品ID | 商品标识符 |
| cid | 商家ID | 商家标识符 |
| keyword | 关键字 | 触发关键词 |
| desc | 商品介绍 | 商品描述信息 |
| price | 商品价格 | 商品价格 |
| stock | 商品库存 | 商品库存数量 |
| sales | 商品销售量 | 商品销售数量 |
| fileId | 文件ID | 文件标识符 |
| size | 文件大小 | 文件大小（字节） |

**注意**: 如果item_info中包含未定义的字段，将直接显示原始字段名。

## 处理逻辑

### 1. 数据映射
- **contentName**: 使用顶级 `name` 字段，如果为空则使用 `domain`
- **contentId**: 优先从 `item_info.tid` 获取，其次 `item_info.fileId`、`item_info.cid`，最后使用 `domain`
- **contentUser**: 使用 `domain` 字段
- **contentTime**: 使用顶级 `create_time` 字段
- **hitKeyword**: 从 `item_info.keyword` 获取

### 2. 告警机制
- **即时告警**: 线索消息接收后立即发送告警，不需要等待AI识别
- **钉钉通知**: 自动发送钉钉机器人消息
- **曙光英雄特殊处理**: 如果关键词包含"曙光"，会额外发送到曙光英雄专用群

### 3. AI识别
- 线索类型的消息**不会**触发AI文本识别流程
- 直接跳过AI检测和去重处理

## 告警模板
钉钉消息模板包含以下信息：
- 来源：线索监控
- 关键词（从item_info.keyword获取，为空时不显示）
- 来源网站（domain）
- 商品名（顶级name字段）
- 链接
- 上架时间（顶级create_time字段，为空时不显示）
- 详细信息（使用中文字段名显示item_info中的所有字段）

### 告警消息示例
```
#### JJSIS 服务通知

来源： 线索监控

关键词： 曙光

来源网站： lilingyu1

商品名： 🔥iOS曙光英雄【黑豹】天卡/全图内透/免签名

链接： https://li.lilingyu1.cn

上架时间： 2025-07-31 14:45:08

详细信息：
- 商品ID： 46782
- 商家ID： 677
- 关键字： 曙光
- 商品介绍： iOS官方网盘：https://ios888.icu<br />...
- 商品价格： 15.38
- 商品库存： 28
- 商品销售量： 0
- 文件ID： 12967460
- 文件大小： 0

此消息发送时间：2025-07-31 15:30:00
```

## 配置要求
需要在配置文件中设置以下参数：
- `app-config.robot.enemy.msg.token`: 钉钉机器人token
- `app-config.robot.enemy.msg.tokenOfShuguang`: 曙光英雄专用群token

## 使用示例
发送POST请求到敌情线索接口，包含上述JSON格式的数据即可触发处理流程。

## 变更说明
相比之前的版本，主要变更：
1. `name`、`create_time` 字段提升到顶级
2. `keyword` 字段移动到 `item_info` 内部
3. 移除了 `fileName` 等文件相关字段
4. 调整了字段映射和处理逻辑
