# 线索(xiansuo)功能说明

## 概述
线索功能用于处理来自各种渠道的线索信息，支持动态的商品信息字段和即时告警功能。

## 数据结构

### 输入数据格式
```json
{
    "item_info": {
        "price": 15.38,
        "name": "🔥iOS曙光英雄【黑豹】天卡/全图内透/免签名",
        "stock": "28",
        "tid": "46782",
        "sales": "0",
        "cid": "677",
        "desc": "iOS官方网盘：https://ios888.icu<br /> iOS官方网盘2：https://zs777.icu<br /> <br />"
    },
    "domain": "lilingyu1",
    "channel": "xiansuo",
    "keyword": "曙光",
    "url": "https://li.lilingyu1.cn/?v=1753259583620",
    "create_time": "2025-05-02T01:21:22+08:00"
}
```

### 字段说明
- **channel**: 固定为 "xiansuo"
- **keyword**: 触发关键词
- **domain**: 域名信息
- **url**: 链接地址
- **create_time**: 创建时间（顶级字段，ISO 8601格式）
- **item_info**: 商品信息对象，字段可根据domain动态变化

## 处理逻辑

### 1. 数据映射
- **contentName**: 优先从 `item_info.name` 获取，其次 `item_info.title`、`item_info.fileName`，最后使用 `domain`
- **contentId**: 优先从 `item_info.id` 获取，其次 `item_info.fileId`、`item_info.tid`、`item_info.cid`，最后使用 `domain`
- **contentUser**: 使用 `domain` 字段
- **contentTime**: 优先使用顶级 `create_time` 字段，如果没有则从 `item_info.createTime`、`item_info.time`、`item_info.publish_time` 中获取

### 2. 告警机制
- **即时告警**: 线索消息接收后立即发送告警，不需要等待AI识别
- **钉钉通知**: 自动发送钉钉机器人消息
- **曙光英雄特殊处理**: 如果关键词包含"曙光"，会额外发送到曙光英雄专用群

### 3. AI识别
- 线索类型的消息**不会**触发AI文本识别流程
- 直接跳过AI检测和去重处理

## 告警模板
钉钉消息模板包含以下信息：
- 来源：线索
- 关键词
- 域名
- 链接
- 商品名
- 上架时间
- 原始创建时间
- 详细信息（动态显示item_info中的所有字段）

## 配置要求
需要在配置文件中设置以下参数：
- `app-config.robot.enemy.msg.token`: 钉钉机器人token
- `app-config.robot.enemy.msg.tokenOfShuguang`: 曙光英雄专用群token

## 使用示例
发送POST请求到敌情线索接口，包含上述JSON格式的数据即可触发处理流程。
