# 线索(xiansuo)功能说明

## 概述
线索功能用于处理来自各种渠道的线索信息，支持动态的商品信息字段和即时告警功能。

## 数据结构

### 输入数据格式
```json
{
    "item_info": {
        "fileId": 19933564,
        "create_time": "2025-05-02T01:21:22+08:00",
        "fileName": "曙光英雄官服内透直装.apk",
        "size": 1930300339,
        "tid": "46782",
        "cid": "677",
        "name": "🔥iOS曙光英雄【黑豹】天卡/全图内透/免签名",
        "desc": "iOS官方网盘：https://ios888.icu",
        "price": 15.38,
        "stock": "28",
        "sales": "0"
    },
    "domain": "lilingyu1",
    "channel": "xiansuo",
    "keyword": "曙光",
    "url": "https://li.lilingyu1.cn/?v=1753259583620",
    "create_time": "2025-05-02T01:21:22+08:00"
}
```

### 字段说明
- **channel**: 固定为 "xiansuo"
- **keyword**: 触发关键词
- **domain**: 域名信息
- **url**: 链接地址
- **create_time**: 创建时间（顶级字段，ISO 8601格式）
- **item_info**: 商品信息对象，字段可根据domain动态变化

### item_info字段映射
系统会将item_info中的英文字段名映射为中文显示名称：

| 英文字段名 | 中文显示名称 | 说明 |
|-----------|-------------|------|
| fileId | 文件ID | 文件标识符 |
| create_time | 上架时间 | 商品上架时间 |
| fileName | 文件名 | 文件名称 |
| size | 文件大小 | 文件大小（字节） |
| tid | 商品ID | 商品标识符 |
| cid | 商家ID | 商家标识符 |
| name | 商品名称 | 商品名称 |
| desc | 商品介绍 | 商品描述信息 |
| price | 商品价格 | 商品价格 |
| stock | 商品库存 | 商品库存数量 |
| sales | 商品销售量 | 商品销售数量 |

**注意**: 如果item_info中包含未定义的字段，将直接显示原始字段名。

## 处理逻辑

### 1. 数据映射
- **contentName**: 优先从 `item_info.name` 获取，其次 `item_info.title`、`item_info.fileName`，最后使用 `domain`
- **contentId**: 优先从 `item_info.id` 获取，其次 `item_info.fileId`、`item_info.tid`、`item_info.cid`，最后使用 `domain`
- **contentUser**: 使用 `domain` 字段
- **contentTime**: 优先使用顶级 `create_time` 字段，如果没有则从 `item_info.createTime`、`item_info.time`、`item_info.publish_time` 中获取

### 2. 告警机制
- **即时告警**: 线索消息接收后立即发送告警，不需要等待AI识别
- **钉钉通知**: 自动发送钉钉机器人消息
- **曙光英雄特殊处理**: 如果关键词包含"曙光"，会额外发送到曙光英雄专用群

### 3. AI识别
- 线索类型的消息**不会**触发AI文本识别流程
- 直接跳过AI检测和去重处理

## 告警模板
钉钉消息模板包含以下信息：
- 来源：线索
- 关键词
- 域名
- 链接
- 商品名
- 上架时间
- 详细信息（使用中文字段名显示item_info中的所有字段）

### 告警消息示例
```
#### JJSIS 服务通知

来源： 线索

关键词： 曙光

域名： lilingyu1

链接： https://li.lilingyu1.cn/?v=1753259583620

商品名： 🔥iOS曙光英雄【黑豹】天卡/全图内透/免签名

上架时间： 2025-05-02T01:21:22+08:00

详细信息：
- 文件ID： 19933564
- 上架时间： 2025-05-02T01:21:22+08:00
- 文件名： 曙光英雄官服内透直装.apk
- 文件大小： 1930300339
- 商品ID： 46782
- 商家ID： 677
- 商品名称： 🔥iOS曙光英雄【黑豹】天卡/全图内透/免签名
- 商品介绍： iOS官方网盘：https://ios888.icu
- 商品价格： 15.38
- 商品库存： 28
- 商品销售量： 0

此消息发送时间：2025-05-02 10:30:00
```

## 配置要求
需要在配置文件中设置以下参数：
- `app-config.robot.enemy.msg.token`: 钉钉机器人token
- `app-config.robot.enemy.msg.tokenOfShuguang`: 曙光英雄专用群token

## 使用示例
发送POST请求到敌情线索接口，包含上述JSON格式的数据即可触发处理流程。
