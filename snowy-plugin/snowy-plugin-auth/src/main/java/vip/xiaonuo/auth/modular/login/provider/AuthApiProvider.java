package vip.xiaonuo.auth.modular.login.provider;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.stereotype.Service;
import vip.xiaonuo.auth.api.AuthApi;
import vip.xiaonuo.auth.modular.login.service.AuthService;

import javax.annotation.Resource;

/**
 * 权限认证API接口提供者
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/3/10 15:23
 */
@Service
@DS("master")
public class AuthApiProvider implements AuthApi {
    @Resource
    private AuthService authService;

    @Override
    public String defaultSysToken() {
        return authService.defaultSysToken();
    }
}
