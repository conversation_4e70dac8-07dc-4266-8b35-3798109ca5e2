#### **JJSIS 服务通知**

**来源：** 线索

**关键词：** ${(hitKeyword)!""}

**域名：** ${(contentUser)!""}

**链接：** [${(contentLink)!""}](${(contentLink)!""})

**商品名：** ${(contentName)!""}

**上架时间：** ${(contentTime)!""}

<#if data.item_info??>
**详细信息：**
<#list data.item_info?keys as key>
<#assign fieldName = key>
<#if key == "fileId">
<#assign fieldName = "文件ID">
<#elseif key == "create_time">
<#assign fieldName = "上架时间">
<#elseif key == "fileName">
<#assign fieldName = "文件名">
<#elseif key == "size">
<#assign fieldName = "文件大小">
<#elseif key == "tid">
<#assign fieldName = "商品ID">
<#elseif key == "cid">
<#assign fieldName = "商家ID">
<#elseif key == "name">
<#assign fieldName = "商品名称">
<#elseif key == "desc">
<#assign fieldName = "商品介绍">
<#elseif key == "price">
<#assign fieldName = "商品价格">
<#elseif key == "stock">
<#assign fieldName = "商品库存">
<#elseif key == "sales">
<#assign fieldName = "商品销售量">
</#if>
- **${fieldName}：** ${(data.item_info[key])!""}
</#list>
<#else>
**详细信息：** 无
</#if>

此消息发送时间：${(sendTime)!""}