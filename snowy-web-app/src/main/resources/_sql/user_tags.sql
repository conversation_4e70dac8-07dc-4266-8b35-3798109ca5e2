CREATE DATABASE IF NOT EXISTS user_tags DEFAULT CHARACTER SET utf8 DEFAULT COLLATE utf8_general_ci;

DROP TABLE IF EXISTS `TB_TAGS_RULE`;
CREATE TABLE `TB_TAGS_RULE` (
  `rule_id` varchar(100) NOT NULL COMMENT '标签规则ID',
  `data_type` varchar(255) NOT NULL COMMENT '数据类型(名命空间)，=mps MPS回传数据，=dps DPS回传数据',
  `tag_dim` varchar(255) DEFAULT NULL COMMENT '标签维度，=account 账号，=device 设备',
  `tag_name_cn` varchar(255) DEFAULT NULL COMMENT '标签名称',
  `tag_name_en` varchar(255) DEFAULT NULL COMMENT '标签英文名',
  `rule_name` varchar(255) DEFAULT NULL COMMENT '规则名称',
  `rule_condition_json` longtext DEFAULT NULL COMMENT '规则条件,JSONString',
  `rule_condition_drl` longtext DEFAULT NULL COMMENT '规则条件drl',
  `tag_status` varchar(255) NOT NULL COMMENT '标签状态，=test测试，=try试验，=online生产',
  `description` longtext DEFAULT NULL COMMENT '描述',
  `create_user` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(255) DEFAULT NULL COMMENT '最后更新人',
  `update_time` datetime DEFAULT NULL COMMENT '最后更新时间',
  `status` varchar(255) NOT NULL COMMENT '状态，-1删除，0无效，1有效',
  PRIMARY KEY (`rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='风控标签-标签规则表';

