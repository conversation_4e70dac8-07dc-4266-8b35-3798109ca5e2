package vip.xiaonuo.common.util;

/**
 * 生成命令：local_test.exe -dbase64 "4mdFpJJuowgDDvdLUqgkef=="
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/12/8 17:51
 */
public class HexToByteUtil {

    public static String keyCreateScreenshot = "c3 13 15 8b 72 cf 46 f4 59 31 72 93 73 61 a1 e6";
    public static String ivCreateScreenshot = "22 6e cb 81 4a 0 33 5c 79 bb 70 af 14 cc ae 2d";

    public static String keyUploadFiles = "ea d6 67 3b 5d 6e 3e 14 57 5c d6 47 c8 44 5e 81";
    public static String ivUploadFiles = "82 7e 3a 87 7a 88 86 25 e 1b 9e 1c 85 db 1a b7";

    public static String keyUploadLargeFiles = "26 D2 B5 6A 3C 8E 46 D8 AD A6 E6 13 7D 99 59 DD";
    public static String ivUploadLargeFiles = "AB D0 EC 61 AC 42 64 1F 56 AC 1D 2C A4 D1 CE 54";

    public static byte[] toByte(String str) {
        String[] strArr = str.split(" ");
        byte[] byteArr = new byte[strArr.length];
        for (int i = 0; i < strArr.length; i++) {
            byteArr[i] = (byte) Integer.parseInt(strArr[i], 16);
        }
        return byteArr;
    }
}
