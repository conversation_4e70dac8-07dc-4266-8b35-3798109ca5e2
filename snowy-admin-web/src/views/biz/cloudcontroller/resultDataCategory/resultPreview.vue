<template>
	<a-modal
		v-model:visible="visible"
		:title="getModalTitle()"
		:width="1200"
		:destroy-on-close="true"
		:confirm-loading="submitLoading"
		@ok="onClose"
		@cancel="onClose"
		:footer="null"
	>
		<a-spin :spinning="submitLoading" tip="加载中...">
			<div class="preview-container">
				<!-- 图片预览区域 -->
				<div class="image-container">
					<a-image
						:src="picData.downloadPath"
						style="max-width: 100%; max-height: 600px;"
					/>
				</div>

				<!-- 控制按钮区域 -->
				<div class="control-bar">
					<div class="navigation-buttons">
						<a-button
							:disabled="!previousRecord"
							@click="previousImage"
							type="primary"
							ghost
						>
							<template #icon><LeftOutlined /></template>
							上一张
						</a-button>

						<span class="image-counter">
							{{ currentIndex + 1 }} / {{ totalCount }}
						</span>

						<a-button
							:disabled="!nextRecord"
							@click="nextImage"
							type="primary"
							ghost
						>
							下一张
							<template #icon><RightOutlined /></template>
						</a-button>
					</div>

					<div class="action-buttons">
						<a-tooltip title="支持键盘快捷键(←→切换，ESC关闭)">
							<a-button type="text" size="small" style="color: #999;">
								<template #icon><QuestionCircleOutlined /></template>
							</a-button>
						</a-tooltip>
						<a-button @click="downloadImage" type="primary" ghost>
							<template #icon><DownloadOutlined /></template>
							下载
						</a-button>
						<a-button @click="onClose" type="primary">
							关闭
						</a-button>
					</div>
				</div>

				<!-- 图片信息区域 -->
				<div class="image-info" v-if="currentRecord">
					<a-descriptions :column="2" size="small" bordered>
						<a-descriptions-item label="主机名">{{ currentRecord.hostName }}</a-descriptions-item>
						<a-descriptions-item label="设备ID">{{ currentRecord.deviceId }}</a-descriptions-item>
						<a-descriptions-item label="异常分类">
							<template v-if="currentRecord.luaName === 'create_screen_shot'">
								<template v-for="textValue in JSON.parse(currentRecord.abResult || '[]')" :key="textValue">
									<a-tag v-if="textValue === '5'" color="#87d068" style="margin-bottom: 4px">
										{{ $TOOL.dictTypeData('cloud_controller_ab_result', textValue) }}
									</a-tag>
									<a-tag v-else-if="['1', '2', '3', '4', '6', '7', '8', '9', '10', '11', '12'].includes(textValue)" color="#f50" style="margin-bottom: 4px">
										{{ $TOOL.dictTypeData('cloud_controller_ab_result', textValue) }}
									</a-tag>
									<a-tag v-else-if="textValue === '-2'" style="margin-bottom: 4px" color="#ffe60f">
										{{ $TOOL.dictTypeData('cloud_controller_ab_result', textValue) }}
									</a-tag>
									<a-tag v-else-if="textValue === '-1'" style="margin-bottom: 4px">
										{{ $TOOL.dictTypeData('cloud_controller_ab_result', textValue) }}
									</a-tag>
									<a-tag v-else style="margin-bottom: 4px">
										未识别
									</a-tag>
								</template>
							</template>
						</a-descriptions-item>
						<a-descriptions-item label="创建时间">{{ currentRecord.createTime }}</a-descriptions-item>
						<a-descriptions-item label="OCR内容" :span="2">{{ currentRecord.ocrContent }}</a-descriptions-item>
					</a-descriptions>
				</div>
			</div>
		</a-spin>
	</a-modal>
</template>

<script setup>
    import { message } from 'ant-design-vue'
    import { LeftOutlined, RightOutlined, DownloadOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue'
	import fileApi from '@/api/dev/fileApi'
	import cloudControllerResultApi from '@/api/biz/cloudControllerResultApi'

	// 默认是关闭状态
	let visible = $ref(false)
    const submitLoading = ref(false)
	const picData = ref({})
	const currentRecord = ref(null)
	const previousRecord = ref(null)
	const nextRecord = ref(null)
	const totalCount = ref(0)
	const currentIndex = ref(0)
	const searchContext = ref({})

	// 异步加载状态
	const isLoadingContext = ref(false)

	// 打开弹框
	const onOpen = (record, context) => {
		visible = true;
		searchContext.value = context || {};

		// 立即设置当前记录并加载图片（利用传入的record）
		currentRecord.value = record;
		setVisible(record);

		// 异步加载截图上下文（获取上一张、下一张信息）
		loadScreenshotContextAsync(record.resultId);

		// 添加键盘事件监听
		document.addEventListener('keydown', handleKeydown);
	}

	// 关闭弹框
	const onClose = () => {
		visible = false;
		picData.value = {};
		currentRecord.value = null;
		previousRecord.value = null;
		nextRecord.value = null;
		totalCount.value = 0;
		currentIndex.value = 0;
		searchContext.value = {};
		isLoadingContext.value = false;

		// 移除键盘事件监听
		document.removeEventListener('keydown', handleKeydown);
	}

	// 异步加载截图上下文
	const loadScreenshotContextAsync = (resultId) => {
		// 防止重复加载
		if (isLoadingContext.value) {
			return;
		}

		isLoadingContext.value = true;
		const params = {
			...searchContext.value.searchFormState,
			category: searchContext.value.category || 'POLICY',
			resultId: resultId
		};

		// 异步加载，不显示loading状态
		cloudControllerResultApi.screenshotContext(params).then((result) => {
			if (result) {
				previousRecord.value = result.previousRecord;
				nextRecord.value = result.nextRecord;
				totalCount.value = result.totalCount || 0;
				currentIndex.value = result.currentIndex || 0;
			}
		}).catch((error) => {
			console.error('异步加载截图上下文失败:', error);
			// 异步加载失败时不显示错误信息，避免干扰用户
		}).finally(() => {
			isLoadingContext.value = false;
		});
	}

    // 预览图片
    const setVisible = (record) => {
		if (!record || !record.fileId) {
			message.error('文件ID不存在');
			return;
		}

		submitLoading.value = true;
		const param = { id: record.fileId };
		fileApi.fileDetail(param).then((res) => {
			picData.value = res;
		}).catch((error) => {
			console.error('加载图片失败:', error);
			message.error('加载图片失败');
		}).finally(() => {
			submitLoading.value = false;
		});
    }

	// 上一张图片
	const previousImage = () => {
		if (previousRecord.value) {
			// 立即切换到上一张记录并显示图片
			const targetRecord = previousRecord.value;
			currentRecord.value = targetRecord;
			setVisible(targetRecord);

			// 异步加载新的上下文信息
			loadScreenshotContextAsync(targetRecord.resultId);
		}
	}

	// 下一张图片
	const nextImage = () => {
		if (nextRecord.value) {
			// 立即切换到下一张记录并显示图片
			const targetRecord = nextRecord.value;
			currentRecord.value = targetRecord;
			setVisible(targetRecord);

			// 异步加载新的上下文信息
			loadScreenshotContextAsync(targetRecord.resultId);
		}
	}

	// 下载图片
	const downloadImage = () => {
		if (picData.value.downloadPath) {
			const $link = document.createElement("a");
			$link.href = picData.value.downloadPath;
			$link.download = picData.value.name || 'screenshot.jpg';
			$link.click();
			document.body.appendChild($link);
			document.body.removeChild($link);
			window.URL.revokeObjectURL($link.href);
		} else {
			message.error('图片下载地址不存在');
		}
	}

	// 获取模态框标题
	const getModalTitle = () => {
		if (picData.value.name && picData.value.sizeInfo) {
			return `${picData.value.name} (${picData.value.sizeInfo})`;
		}
		return '截图预览';
	}

	// 键盘事件处理
	const handleKeydown = (event) => {
		if (!visible) return;

		switch (event.key) {
			case 'ArrowLeft':
				event.preventDefault();
				previousImage();
				break;
			case 'ArrowRight':
				event.preventDefault();
				nextImage();
				break;
			case 'Escape':
				event.preventDefault();
				onClose();
				break;
		}
	}

	// 调用这个函数将子组件的一些数据和方法暴露出去
	defineExpose({
		onOpen
	})
</script>

<style scoped>
.preview-container {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.image-container {
	text-align: center;
	background-color: #f5f5f5;
	padding: 20px;
	border-radius: 8px;
	min-height: 400px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.control-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px 0;
	border-top: 1px solid #f0f0f0;
	border-bottom: 1px solid #f0f0f0;
}

.navigation-buttons {
	display: flex;
	align-items: center;
	gap: 16px;
}

.image-counter {
	font-weight: 500;
	color: #666;
	min-width: 80px;
	text-align: center;
}

.action-buttons {
	display: flex;
	gap: 8px;
}

.image-info {
	margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.control-bar {
		flex-direction: column;
		gap: 12px;
	}

	.navigation-buttons {
		order: 2;
	}

	.action-buttons {
		order: 1;
	}
}
</style>
