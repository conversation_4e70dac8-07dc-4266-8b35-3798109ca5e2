<template>
	<a-drawer
		title="详情"
		:width="600"
		:visible="visible"
		:destroy-on-close="true"
		:footer-style="{ textAlign: 'right' }"
		@close="onClose"
	>
		<a-descriptions :column="1" size="small" bordered class="mb-2" :title="formData.hostName">

			<a-descriptions-item label="Lua任务名">{{ formData.luaName }}</a-descriptions-item>
			<a-descriptions-item label="设备ID">{{ formData.deviceId }}</a-descriptions-item>
			<a-descriptions-item label="主机名">{{ formData.hostName }}</a-descriptions-item>
			<a-descriptions-item label="策略ID"><a-tag v-for="textValue in JSON.parse(formData.policyIds)" :key="textValue" color="green">{{ textValue }}</a-tag></a-descriptions-item>
			<a-descriptions-item label="记录ID"><a-tag v-for="textValue in JSON.parse(formData.recordIds)" :key="textValue" color="blue">{{ textValue }}</a-tag></a-descriptions-item>
			<a-descriptions-item label="异常分类">
                <template v-if="formData.luaName === 'create_screen_shot'">
                <template v-for="textValue in JSON.parse(formData.abResult)">
					<a-tag v-if="textValue === '5'" color="#87d068" style="margin-bottom: 4px">
						{{ $TOOL.dictTypeData('cloud_controller_ab_result', textValue) }}
					</a-tag>
					<a-tag v-else-if="textValue === '1' || textValue === '2' || textValue === '3' || textValue === '4' || textValue === '6' || textValue === '7' || textValue === '8' || textValue === '9' || textValue === '10' || textValue === '11' || textValue === '12'" color="#f50" style="margin-bottom: 4px">
						{{ $TOOL.dictTypeData('cloud_controller_ab_result', textValue) }}
					</a-tag>
					<a-tag v-else-if="textValue === '-2'" style="margin-bottom: 4px" color="#ffe60f">
						{{ $TOOL.dictTypeData('cloud_controller_ab_result', textValue) }}
					</a-tag>
					<a-tag v-else-if="textValue === '-1'" style="margin-bottom: 4px">
						{{ $TOOL.dictTypeData('cloud_controller_ab_result', textValue) }}
					</a-tag>
					<a-tag v-else style="margin-bottom: 4px">
						未识别
					</a-tag>
                </template>
                </template>
			</a-descriptions-item>
			<a-descriptions-item label="异常原因">{{ formData.abContent }}</a-descriptions-item>
			<a-descriptions-item label="OCR内容">{{ formData.ocrContent }}</a-descriptions-item>
			<a-descriptions-item label="备注">{{ formData.remark }}</a-descriptions-item>
			<a-descriptions-item label="创建时间">{{ formData.createTime }}</a-descriptions-item>
			<a-descriptions-item label="最后更新时间">{{ formData.updateTime }}</a-descriptions-item>
			<a-descriptions-item label="状态">
					<a-tag v-if="formData.status === '1'" color="#87d068">
						{{ $TOOL.dictTypeData('status', formData.status) }}
					</a-tag>
					<a-tag v-if="formData.status === '0'" color="#f50">
						{{ $TOOL.dictTypeData('status', formData.status) }}
					</a-tag>
					<a-tag v-if="formData.status === '-1'" color="#108ee9">
						{{ $TOOL.dictTypeData('status', formData.status) }}
					</a-tag>
			</a-descriptions-item>
		</a-descriptions>

		<a-space direction="vertical" class="mb-2" style="width: 100%">
			数据：
			<XnHighlightjs language="JSON" :code="dataJson" copy></XnHighlightjs>
		</a-space>
	</a-drawer>
</template>

<script setup name="resultDetail">
	// 默认是关闭状态
	let visible = $ref(false)
	const formData = ref({})
	const table = ref()
	const dataJson = ref()
	// 打开抽屉
	const onOpen = (record) => {
		visible = true
		formData.value = record
		if (record.dataJson) {
			 const code = JSON.parse(record.dataJson);
			 try{
			 if(code.luaWindowNameMap&&code.luaWindowNameMap.length!=0){
			 	for (let i = 0; i < code.luaWindowNameMap.length; i++) {
                    code.luaWindowNameMap[i] = JSON.parse(code.luaWindowNameMap[i]);
                }
			 }
			 if(code.luaPolicyIdMatch&&code.luaPolicyIdMatch.length!=0){
			 	for (let i = 0; i < code.luaPolicyIdMatch.length; i++) {
                    code.luaPolicyIdMatch[i] = JSON.parse(code.luaPolicyIdMatch[i]);
                }
			 }
			 if(code.luaDirectoryInfo&&code.luaDirectoryInfo.length!=0){
			 	for (let i = 0; i < code.luaDirectoryInfo.length; i++) {
                    code.luaDirectoryInfo[i] = JSON.parse(code.luaDirectoryInfo[i]);
                }
			 }
			 if(code.luaPolicyIdExtMatch&&code.luaPolicyIdExtMatch.length!=0){
			 	for (let i = 0; i < code.luaPolicyIdExtMatch.length; i++) {
                    code.luaPolicyIdExtMatch[i] = JSON.parse(code.luaPolicyIdExtMatch[i]);
                }
			 }
			 if(code.luaFirewallRes&&code.luaFirewallRes.length!=0){
			 	for (let i = 0; i < code.luaFirewallRes.length; i++) {
                    code.luaFirewallRes[i] = JSON.parse(code.luaFirewallRes[i]);
                }
			 }

			} catch (e) {
			}
			 dataJson.value = JSON.stringify(code, undefined, 2);
		//	dataJson.value = record.dataJson
		} else {
			dataJson.value = '无'
		}
	}
	// 关闭抽屉
	const onClose = () => {
		dataJson.value = ''
		visible = false
	}
	// 调用这个函数将子组件的一些数据和方法暴露出去
	defineExpose({
		onOpen
	})
</script>
