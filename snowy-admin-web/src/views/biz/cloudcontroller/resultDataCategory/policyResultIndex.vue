<template>
	<a-row>
		<a-col :span="4">
			<a-tree
				v-if="treeData.records.length > 0"
				v-model:expandedKeys="defaultExpandedKeys"
				:tree-data="treeData.records"
				:field-names="treeFieldNames"
				@select="treeSelect"
			>
			</a-tree>
			<a-empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
			<a-pagination size="small" :current="treeData.current" :total="treeData.total" :pageSize="treeData.size" @change="loadTreeData" :show-total="(total, range) => `${range[0]}-${range[1]} 共 ${total} 条`" showSizeChanger showLessItems/>
		</a-col>
		<a-col :span="20">
			<s-table
				ref="table"
				:columns="columns"
				:data="loadData"
				:expand-row-by-click="true"
				bordered
            	:row-key="(record) => record.resultId"
			>
				<template #operator class="table-operator">
					<a-form ref="searchFormRef" name="advanced_search" class="ant-advanced-search-form" :model="searchFormState">
						<a-row :gutter="24">
							<a-col :span="5">
								<a-form-item name="luaName" label="Lua任务名">
									<a-select v-model:value="searchFormState.luaName" placeholder="请选择Lua任务名" :options="luaNameOptions" optionFilterProp="luaTaskNameZh" :field-names="{ label: 'luaTaskNameZh', value: 'luaTaskNameEn' }" show-search allow-clear/>
								</a-form-item>
							</a-col>
							<a-col :span="5">
								<a-form-item label="黑产设备" name="deviceId">
									<a-select v-model:value="searchFormState.deviceId" placeholder="请选择黑产设备" :options="deviceIdAliasOptions"/>
								</a-form-item>
							</a-col>
							<a-col :span="3">
								<a-form-item label="主机名" name="hostName">
									<a-input v-model:value="searchFormState.hostName" placeholder="请输入主机名" />
								</a-form-item>
							</a-col>
							<a-col :span="4">
								<a-form-item name="abResult" label="异常分类">
									<a-select v-model:value="searchFormState.abResult" placeholder="请选择异常分类" :options="abResultOptions"/>
								</a-form-item>
							</a-col>
							<a-col :span="3">
								<a-form-item label="OCR内容" name="ocrContent">
									<a-input v-model:value="searchFormState.ocrContent" placeholder="请输入OCR识别内容" />
								</a-form-item>
							</a-col>
							<template v-if="advanced">
								<a-col :span="4">
									<a-form-item label="数据" name="dataJson">
										<a-input v-model:value="searchFormState.dataJson" placeholder="请输入数据内容" />
									</a-form-item>
								</a-col>
								<a-col :span="20">
								</a-col>
							</template>

							<a-col :span="4">
								<a-button type="primary" @click="table.refresh(true)">查询</a-button>
								<a-button style="margin: 0 8px" @click="() => searchFormRef.resetFields()">重置</a-button>
								<a @click="toggleAdvanced" style="margin-left: 8px">
									{{ advanced ? '收起' : '展开' }}
									<component :is="advanced ? 'up-outlined' : 'down-outlined'"/>
								</a>
							</a-col>

						</a-row>
					</a-form>
				</template>
				<template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'remark' && $TOOL.dictTypeData('cloud_controller_deviceid_alias', record.deviceId) !=='无此字典'">
                	<!-- <a-tag color="#ffbf00">{{ $TOOL.dictTypeData('cloud_controller_deviceid_alias', record.deviceId) }}</a-tag> -->
                	<!-- <a-tag :color="'#' + record.deviceId.slice(0, 6)">{{ $TOOL.dictTypeData('cloud_controller_deviceid_alias', record.deviceId) }}</a-tag> -->
                	<a-tag :color="getColorByDeviceId(record.deviceId)">{{ $TOOL.dictTypeData('cloud_controller_deviceid_alias', record.deviceId) }}</a-tag>
                </template>
                <template v-if="column.dataIndex === 'abResult' && record.luaName === 'create_screen_shot'">
                <template v-for="textValue in JSON.parse(record.abResult)">
					<a-tag v-if="textValue === '5'" color="#87d068" style="margin-bottom: 4px">
						{{ $TOOL.dictTypeData('cloud_controller_ab_result', textValue) }}
					</a-tag>
					<a-tag v-else-if="textValue === '1' || textValue === '2' || textValue === '3' || textValue === '4' || textValue === '6' || textValue === '7' || textValue === '8' || textValue === '9' || textValue === '10' || textValue === '11' || textValue === '12'" color="#f50" style="margin-bottom: 4px" >
						{{ $TOOL.dictTypeData('cloud_controller_ab_result', textValue) }}
					</a-tag>
					<a-tag v-else-if="textValue === '-2'" style="margin-bottom: 4px" color="#ffe60f">
						{{ $TOOL.dictTypeData('cloud_controller_ab_result', textValue) }}
					</a-tag>
					<a-tag v-else-if="textValue === '-1'" style="margin-bottom: 4px" >
						{{ $TOOL.dictTypeData('cloud_controller_ab_result', textValue) }}
					</a-tag>
					<a-tag v-else style="margin-bottom: 4px" >
						未识别
					</a-tag>
                </template>
                </template>
					<template v-if="column.dataIndex === 'policyIds'">
						<a-tag v-for="textValue in JSON.parse(record.policyIds)" :key="textValue" color="blue">{{ textValue }}</a-tag>
					</template>
					<template v-if="column.dataIndex === 'recordIds'">
						<a-tag v-for="textValue in JSON.parse(record.recordIds)" :key="textValue" color="green">{{ textValue }}</a-tag>
					</template>
					<template v-if="column.dataIndex === 'action'">
						<a @click="detailRef.onOpen(record)">详情</a>
                        <a-divider type="vertical" v-if="record.fileId" />
						<a @click="fileDownload(record)" v-if="record.fileId">下载</a>
                        <a-divider type="vertical" v-if="record.luaName==='create_screen_shot' && record.fileId" />
                    	<a @click="previewRef.onOpen(record, getScreenshotContext())" v-if="record.luaName==='create_screen_shot' && record.fileId">
							预览
						</a>
					</template>
				</template>
			</s-table>
		</a-col>
	</a-row>
    <detail ref="detailRef" />
   	<preview ref="previewRef" />
</template>

<script setup>
	import { Empty } from 'ant-design-vue'
    import tool from '@/utils/tool'
    import cloudControllerResultApi from '@/api/biz/cloudControllerResultApi'
	import fileApi from '@/api/dev/fileApi'
    import luaTaskApi from '@/api/biz/luaTaskApi'
	import detail from './resultDetail.vue'
	import preview from './resultPreview.vue'
	// 定义tableDOM
	const table = ref(null)
	const form = ref()
	const searchFormRef = ref()
	let searchFormState = reactive({})
	// 默认展开的节点
	let defaultExpandedKeys = ref([])
	const treeData = ref({records:[]})
	// 替换treeNode 中 title,key,children
	const treeFieldNames = { children: 'children', title: 'title', key: 'key' }
    const luaNameOptions = ref([])
	const detailRef = ref()
	const previewRef = ref()
	// 查询区域显示更多控制
	const advanced = ref(false)
	const toggleAdvanced = () => {
		advanced.value = !advanced.value
	}

    const columns = [
        {
            title: 'Lua任务名',
            dataIndex: 'luaName'
        },
        {
            title: '主机名',
            dataIndex: 'hostName'
        },
        {
            title: '设备ID',
            dataIndex: 'deviceId'
        },
        {
            title: '设备别名',
            dataIndex: 'remark'
        },
        {
            title: '策略ID',
            dataIndex: 'policyIds',
            ellipsis: true
        },
        {
            title: '记录ID',
            dataIndex: 'recordIds',
            ellipsis: true
        },
        {
            title: '异常分类',
            dataIndex: 'abResult'
        },
        {
            title: 'OCR内容',
            dataIndex: 'ocrContent',
            ellipsis: true
        },
        {
            title: '数据',
            dataIndex: 'dataJson',
            ellipsis: true
        },
        {
            title: '创建时间',
            dataIndex: 'createTime'
        },
        {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: '150px'
        }
    ]

	// 表格查询 返回 Promise 对象
	const loadData = (parameter) => {
		loadTreeData();
		loadLuaTaskData();
		parameter.category = 'POLICY'
		return cloudControllerResultApi.resultDataPage(Object.assign(parameter, searchFormState)).then((data) => {
			return data
		})
	}
	// 加载左侧的树
	const loadTreeData = (current, size) => {
		current= current || treeData.value.current;
		size= size || treeData.value.size;
		const param = {
			current:current,
			size:size,
			category: 'POLICY'
		}
		cloudControllerResultApi.policyResultTreePage(param).then((data) => {
			if (data !== null) {
				treeData.value = data
			}
		})
	}
	// 点击树查询
	const treeSelect = (selectedKeys) => {
		if (selectedKeys.length > 0) {
			// 树点击展开
			if(!defaultExpandedKeys.value.includes(selectedKeys.toString())){
				defaultExpandedKeys.value.push(selectedKeys.toString())
			}
			searchFormState.searchKey = selectedKeys.toString()
		} else {
			delete searchFormState.searchKey
		}
		table.value.refresh(true)
	}
    // 下载文件
    const fileDownload = (record) => {
		const param = {
			id: record.fileId
		}
			fileApi.fileDetail(param).then((res) => {
				const $link = document.createElement("a");
				$link.href = res.downloadPath;
				$link.download = res.downloadPath
				$link.click();
				document.body.appendChild($link);
				document.body.removeChild($link); // 下载完成移除元素
				window.URL.revokeObjectURL($link.href); // 释放掉blob对象
			})
    }

    // 获取luaTask列表
    const loadLuaTaskData = (parameter) => {
        luaTaskApi.luaTaskList(parameter).then((data) => {
        if(data){
    		// luaTask去重
    		luaNameOptions.value = [];
    		const luaTaskNameZhSet = new Set();
            data.map((item) => {
				item.luaTaskNameZh=item.luaTaskNameCn + '(' + item.luaTaskNameEn + ')'
				if(!luaTaskNameZhSet.has(item.luaTaskNameZh)){
					luaNameOptions.value.push(item);
					luaTaskNameZhSet.add(item.luaTaskNameZh);
					}
            	})
        }
           // luaNameOptions.value = data
        })
    }
    const abResultOptions = tool.dictList('cloud_controller_ab_result');
    const deviceIdAliasOptions = tool.dictList('cloud_controller_deviceid_alias');
    // 颜色列表
    const colorList = [
		'#7265e6',
		'#ffbf00',
		'#00a2ae',
		'#f56a00',
		'#1890ff',
		'#606d80'
    ];
    // 缓存设备对应颜色
    const colorCache = new Map();
    // 根据设备ID获取颜色
    const getColorByDeviceId = (deviceId) => {
        // 获取缓存的颜色值，如果不存在则计算并存储
        const cached = colorCache.get(deviceId);
        if (cached) {
          return cached;
        }
    	let hashCode = 0;
    	for (let i = 0; i < deviceId.length; i++) {
        	hashCode = (hashCode * 31 + deviceId.charCodeAt(i)) >>> 0;
    	}

    // 将哈希码转换为正数（JavaScript 的哈希码可能是负数）
    // 通过与 2147483647（即 2^31 - 1）进行位与运算来实现
    hashCode = hashCode & 2147483647;

    // 计算索引值
    const index = hashCode % colorList.length;;
    // 返回对应的颜色值
    const color = colorList[index];
    // 存储到缓存
    colorCache.set(deviceId, color);
    return color;
	// return colorList[index];
   // return index;
    }

    // 获取截图上下文信息
    const getScreenshotContext = () => {
        return {
            searchFormState: { ...searchFormState },
            category: 'POLICY'
        }
    }
</script>

<style scoped>
	.cardImp {
		margin-right: 10px;
	}
	.ant-form-item {
		margin-bottom: 16px !important;
	}
	.snowy-buttom-left {
		margin-left: 8px;
	}
</style>
